#include "mydefine.h"
#include "stdio.h"

void oled_task(void)
{
    static uint32_t counter = 0;
    uint8_t buffer[32];

    // 清屏
    OLED_Clear();

    // 检查MPU6050数据是否就绪
    if (mpu6050_is_data_ready())
    {
        float pitch, roll, yaw;
        mpu6050_get_angles(&pitch, &roll, &yaw);

        // 显示角度数据
        sprintf((char*)buffer, "Pitch: %.1f", pitch);
        OLED_ShowString(0, 0, buffer, 12);

        sprintf((char*)buffer, "Roll:  %.1f", roll);
        OLED_ShowString(0, 16, buffer, 12);

        sprintf((char*)buffer, "Yaw:   %.1f", yaw);
        OLED_ShowString(0, 32, buffer, 12);

        sprintf((char*)buffer, "Count: %u", (unsigned int)counter++);
        OLED_ShowString(0, 48, buffer, 12);

        mpu6050_clear_data_ready();
    }
    else
    {
        // MPU6050数据未就绪，显示等待信息
        OLED_ShowString(0, 0, (uint8_t*)"MPU6050 Init...", 12);
        sprintf((char*)buffer, "Count: %u", (unsigned int)counter++);
        OLED_ShowString(0, 16, buffer, 12);
    }
}

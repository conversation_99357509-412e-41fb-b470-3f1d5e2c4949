#include "mydefine.h"
#include "stdio.h"
void oled_task(void)
{
      if (mpu6050_is_data_ready())
        {
            float pitch, roll, yaw;
            mpu6050_get_angles(&pitch, &roll, &yaw);

            uint8_t buffer[32];

            // 清屏
            OLED_Clear();

            // 显示角度数据
            sprintf((char*)buffer, "Pitch: %.1f", pitch);
            OLED_ShowString(0, 0, buffer, 12);

            sprintf((char*)buffer, "Roll:  %.1f", roll);
            OLED_ShowString(0, 16, buffer, 12);

            sprintf((char*)buffer, "Yaw:   %.1f", yaw);
            OLED_ShowString(0, 32, buffer, 12);

            mpu6050_clear_data_ready();
        }
}

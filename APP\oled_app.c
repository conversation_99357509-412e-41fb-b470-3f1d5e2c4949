#include "mydefine.h"
#include "stdio.h"

void oled_task(void)
{
    static uint32_t counter = 0;
    uint8_t buffer[32];

    // 清屏
    OLED_Clear();

    // 显示系统运行状态
    OLED_ShowString(0, 0, (uint8_t*)"System Running", 12);

    sprintf((char*)buffer, "Count: %u", (unsigned int)counter++);
    OLED_ShowString(0, 16, buffer, 12);

    sprintf((char*)buffer, "Time: %lu ms", SystemTime_GetMs());
    OLED_ShowString(0, 32, buffer, 12);

    // 检查MPU6050数据是否就绪
    if (mpu6050_is_data_ready())
    {
        float pitch, roll, yaw;
        mpu6050_get_angles(&pitch, &roll, &yaw);

        sprintf((char*)buffer, "P:%.1f R:%.1f Y:%.1f", pitch, roll, yaw);
        OLED_ShowString(0, 48, buffer, 12);

        mpu6050_clear_data_ready();
    }
    else
    {
        OLED_ShowString(0, 48, (uint8_t*)"MPU6050: Wait", 12);
    }
}

#include "mydefine.h"
#include "stdio.h"

void oled_task(void)
{
    static uint32_t last_update_time = 0;
    static uint32_t counter = 0;
    uint32_t current_time = SystemTime_GetMs();

    // 每100ms更新一次显示
    if (current_time - last_update_time >= 100)
    {
        uint8_t buffer[32];

        // 清屏
        OLED_Clear();

        // 显示系统状态
        OLED_ShowString(0, 0, (uint8_t*)"System Running", 12);

        sprintf((char*)buffer, "Time: %lu ms", current_time);
        OLED_ShowString(0, 16, buffer, 12);

        sprintf((char*)buffer, "Counter: %lu", counter++);
        OLED_ShowString(0, 32, buffer, 12);

        // 显示MPU6050状态
        OLED_ShowString(0, 48, (uint8_t*)"MPU6050: OFF", 12);

        OLED_Refresh();
        last_update_time = current_time;
    }
}

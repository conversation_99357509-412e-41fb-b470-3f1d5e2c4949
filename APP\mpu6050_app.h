#ifndef _MPU6050_APP_H
#define _MPU6050_APP_H

#include "mydefine.h"
#include "mpu6050.h"
#include <stdbool.h>

// MPU6050任务函数
void mpu6050_task(void);

// MPU6050数据状态函数
bool mpu6050_is_data_ready(void);
void mpu6050_clear_data_ready(void);

// MPU6050数据获取函数
void mpu6050_get_angles(float *pitch_val, float *roll_val, float *yaw_val);
void mpu6050_get_gyro_data(short *gyro_x, short *gyro_y, short *gyro_z);
void mpu6050_get_accel_data(short *accel_x, short *accel_y, short *accel_z);

#endif

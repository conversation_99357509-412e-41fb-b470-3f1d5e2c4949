******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jun 26 20:59:22 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004bc1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006550  00019ab0  R  X
  SRAM                  20200000   00008000  000002a2  00007d5e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006550   00006550    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004e30   00004e30    r-x .text
  00004ef0    00004ef0    00001610   00001610    r-- .rodata
  00006500    00006500    00000050   00000050    r-- .cinit
20200000    20200000    000000a4   00000000    rw-
  20200000    20200000    0000006e   00000000    rw- .data
  20200070    20200070    00000034   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004e30     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002c4     oled_hardware_i2c.o (.text.OLED_Init)
                  000013b0    000002b0     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001660    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001880    00000210     mpu6050.o (.text.Read_Quad)
                  00001a90    000001e4     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00001c74    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001e50    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001ff8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000218a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000218c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002314    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00002484    00000154     mspm0_i2c.o (.text.mspm0_i2c_read)
                  000025d8    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  00002728    00000148     inv_mpu.o (.text.mpu_load_firmware)
                  00002870    00000144     oled_hardware_i2c.o (.text.OLED_Clear)
                  000029b4    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002af0    00000138     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00002c28    00000134     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00002d5c    0000012c     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00002e88    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002fa8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000030b4    000000f8     inv_mpu.o (.text.mpu_set_bypass)
                  000031ac    000000f4     inv_mpu.o (.text.mpu_init)
                  000032a0    000000ec     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  0000338c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003470    000000e4     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003554    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00003630    000000dc     inv_mpu.o (.text.mpu_set_sensors)
                  0000370c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000037e4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000038bc    000000d0     inv_mpu.o (.text.mpu_set_dmp_state)
                  0000398c    000000b8     mpu6050.o (.text.MPU6050_Init)
                  00003a44    000000ac     inv_mpu.o (.text.mpu_configure_fifo)
                  00003af0    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003b94    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003c36    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00003cd0    00000090     oled_app.o (.text.oled_task)
                  00003d60    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003dec    0000008c     mspm0_i2c.o (.text.mpu6050_i2c_sda_unlock)
                  00003e78    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003efa    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00003efc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003f78    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  00003ff2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003ff4    00000074                            : comparedf2.c.obj (.text.__gedf2)
                  00004068    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004070    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000040e4    00000074     inv_mpu.o (.text.mpu_set_lpf)
                  00004158    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000041c8    0000006c     inv_mpu.o (.text.mpu_write_mem)
                  00004234    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000429c    00000068     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004304    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000436a    00000002     mpu6050.o (.text.android_orient_cb)
                  0000436c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000043ce    00000062     libc.a : memset16.S.obj (.text:memset)
                  00004430    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000448e    00000002     mpu6050.o (.text.tap_cb)
                  00004490    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000044ec    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00004544    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000459c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000045f2    00000002     --HOLE-- [fill = 0]
                  000045f4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00004648    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000469c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000046f0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004742    00000002     --HOLE-- [fill = 0]
                  00004744    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000478e    00000002     --HOLE-- [fill = 0]
                  00004790    00000044     scheduler.o (.text.scheduler_run)
                  000047d4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00004814    00000040     clock.o (.text.SysTick_Init)
                  00004854    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004894    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000048d4    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004914    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004950    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000498c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000049c8    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00004a02    00000002     --HOLE-- [fill = 0]
                  00004a04    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00004a3c    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004a74    00000034     mpu6050_app.o (.text.mpu6050_task)
                  00004aa8    00000034     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00004adc    00000034     clock.o (.text.mspm0_delay_ms)
                  00004b10    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004b40    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004b6c    0000002c     mpu6050_app.o (.text.mpu6050_get_angles)
                  00004b98    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00004bc0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004be8    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004c0e    00000002     --HOLE-- [fill = 0]
                  00004c10    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004c34    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004c56    00000002     --HOLE-- [fill = 0]
                  00004c58    00000020     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00004c78    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004c96    0000001e     empty.o (.text.main)
                  00004cb4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00004cd0    0000001c     inv_mpu.o (.text.mpu_get_sample_rate)
                  00004cec    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004d04    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00004d1c    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00004d34    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004d4a    00000002     --HOLE-- [fill = 0]
                  00004d4c    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  00004d60    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004d74    00000014     scheduler.o (.text.scheduler_init)
                  00004d88    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004d9c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004dae    00000002     --HOLE-- [fill = 0]
                  00004db0    00000010     interrupt.o (.text.SysTick_Handler)
                  00004dc0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004dd0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004dde    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004dec    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00004df8    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00004e04    0000000c     empty.o (.text.led_blink_task)
                  00004e10    0000000c     mpu6050_app.o (.text.mpu6050_clear_data_ready)
                  00004e1c    0000000c     mpu6050_app.o (.text.mpu6050_is_data_ready)
                  00004e28    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004e32    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004e3c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004e4c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004e56    00000002     --HOLE-- [fill = 0]
                  00004e58    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00004e68    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004e72    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004e7c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004e86    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00004e90    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00004ea0    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00004eaa    00000008            : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004eb2    00000002     --HOLE-- [fill = 0]
                  00004eb4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004ebc    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004ec2    00000002     --HOLE-- [fill = 0]
                  00004ec4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00004ed4    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004eda    00000006            : exit.c.obj (.text:abort)
                  00004ee0    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004ee4    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00004ee8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004eec    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00006500    00000050     
                  00006500    0000002c     (.cinit..data.load) [load image, compression = lzss]
                  0000652c    0000000c     (__TI_handler_table)
                  00006538    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006540    00000010     (__TI_cinit_table)

.rodata    0    00004ef0    00001610     
                  00004ef0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00005ae6    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  000060d6    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  000062fe    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00006300    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006401    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.accel_axes)
                  00006404    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.gyro_axes)
                  00006407    00000001     --HOLE-- [fill = 0]
                  00006408    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00006448    00000028     inv_mpu.o (.rodata.test)
                  00006470    0000001e     inv_mpu.o (.rodata.reg)
                  0000648e    00000018     inv_mpu.o (.rodata.cst8)
                  000064a6    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000064b7    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000064c8    0000000c     inv_mpu.o (.rodata.hw)
                  000064d4    0000000c     oled_app.o (.rodata.str1.10123847253219111340.1)
                  000064e0    0000000c     oled_app.o (.rodata.str1.17288060079586621852.1)
                  000064ec    0000000c     oled_app.o (.rodata.str1.865768355259620247.1)
                  000064f8    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000064fa    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000006e     UNINITIALIZED
                  20200000    0000002c     inv_mpu.o (.data.st)
                  2020002c    00000024     scheduler.o (.data.scheduler_task)
                  20200050    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200054    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200058    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  2020005c    00000004     mpu6050_app.o (.data.mpu6050_task.last_update_time)
                  20200060    00000004     clock.o (.data.sys_tick)
                  20200064    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.2)
                  20200066    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)
                  20200067    00000001     mpu6050_app.o (.data.mpu6050_data_ready)
                  20200068    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  2020006a    00000002     --HOLE--
                  2020006c    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.4)

.bss       0    20200070    00000034     UNINITIALIZED
                  20200070    00000010     (.common:quat)
                  20200080    00000006     (.common:accel)
                  20200086    00000006     (.common:gyro)
                  2020008c    00000004     (.common:pitch)
                  20200090    00000004     (.common:roll)
                  20200094    00000004     (.common:sensor_timestamp)
                  20200098    00000004     (.common:start_time)
                  2020009c    00000004     (.common:yaw)
                  202000a0    00000002     (.common:sensors)
                  202000a2    00000001     (.common:more)
                  202000a3    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             452     4         0      
       startup_mspm0g350x_ticlang.o   6       192       0      
       scheduler.o                    88      0         37     
       empty.o                        42      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         588     196       37     
                                                               
    .\APP\
       oled_app.o                     144     36        0      
       mpu6050_app.o                  120     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         264     36        5      
                                                               
    .\BSP\MPU6050\
       inv_mpu_dmp_motion_driver.o    1812    3068      15     
       inv_mpu.o                      2968    106       44     
       mspm0_i2c.o                    780     0         0      
       mpu6050.o                      716     0         47     
    +--+------------------------------+-------+---------+---------+
       Total:                         6276    3174      106    
                                                               
    .\BSP\OLED\
       oled_hardware_i2c.o            1686    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1686    2072      0      
                                                               
    .\System\
       clock.o                        140     0         8      
       interrupt.o                    36      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         176     0         8      
                                                               
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       192     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         202     0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8096    355       4      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2702    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       80        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   19994   5913      672    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006540 records: 2, size/record: 8, table size: 16
	.data: load addr=00006500, load size=0000002c bytes, run addr=20200000, run size=0000006e bytes, compression=lzss
	.bss: load addr=00006538, load size=00000008 bytes, run addr=20200070, run size=00000034 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000652c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001ff9     00004e3c     00004e3a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000338d     00004e58     00004e54   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004e70          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004e84          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004eb0          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00004ed8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002fa9     00004e90     00004e8e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002003     00004ec4     00004ec0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004ee6          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)

[4 trampolines]
[9 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                           
-------   ----                           
0000218b  ADC0_IRQHandler                
0000218b  ADC1_IRQHandler                
0000218b  AES_IRQHandler                 
00004ee0  C$$EXIT                        
0000218b  CANFD0_IRQHandler              
0000218b  DAC0_IRQHandler                
00004e29  DL_Common_delayCycles          
00004431  DL_I2C_fillControllerTXFIFO    
00004915  DL_I2C_flushControllerTXFIFO   
00004be9  DL_I2C_setClockConfig          
0000218b  DMA_IRQHandler                 
0000218b  Default_Handler                
0000218b  GROUP0_IRQHandler              
00004d4d  GROUP1_IRQHandler              
00004ee1  HOSTexit                       
0000218b  HardFault_Handler              
0000218b  I2C0_IRQHandler                
0000218b  I2C1_IRQHandler                
0000398d  MPU6050_Init                   
0000218b  NMI_Handler                    
00002871  OLED_Clear                     
000010ed  OLED_Init                      
00003555  OLED_ShowChar                  
00003f79  OLED_ShowString                
00002af1  OLED_WR_Byte                   
0000218b  PendSV_Handler                 
0000218b  RTC_IRQHandler                 
00001881  Read_Quad                      
00004ee9  Reset_Handler                  
0000218b  SPI0_IRQHandler                
0000218b  SPI1_IRQHandler                
0000218b  SVC_Handler                    
00004159  SYSCFG_DL_GPIO_init            
000045f5  SYSCFG_DL_I2C_MPU6050_init     
00004649  SYSCFG_DL_I2C_OLED_init        
0000469d  SYSCFG_DL_SYSCTL_init          
00004ced  SYSCFG_DL_init                 
000047d5  SYSCFG_DL_initPower            
00004db1  SysTick_Handler                
00004815  SysTick_Init                   
0000218b  TIMA0_IRQHandler               
0000218b  TIMA1_IRQHandler               
0000218b  TIMG0_IRQHandler               
0000218b  TIMG12_IRQHandler              
0000218b  TIMG6_IRQHandler               
0000218b  TIMG7_IRQHandler               
0000218b  TIMG8_IRQHandler               
0000218b  UART0_IRQHandler               
0000218b  UART1_IRQHandler               
0000218b  UART2_IRQHandler               
0000218b  UART3_IRQHandler               
20208000  __STACK_END                    
00000200  __STACK_SIZE                   
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00006540  __TI_CINIT_Base                
00006550  __TI_CINIT_Limit               
00006550  __TI_CINIT_Warm                
0000652c  __TI_Handler_Table_Base        
00006538  __TI_Handler_Table_Limit       
0000498d  __TI_auto_init_nobinit_nopinit 
00003efd  __TI_decompress_lzss           
00004d9d  __TI_decompress_none           
000044ed  __TI_ltoa                      
ffffffff  __TI_pprof_out_hndl            
000000c1  __TI_printfi                   
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
00000000  __TI_static_base__             
00004d35  __TI_zero_init_nomemset        
00002003  __adddf3                       
000037ef  __addsf3                       
00006300  __aeabi_ctype_table_           
00006300  __aeabi_ctype_table_C          
00004071  __aeabi_d2f                    
00004745  __aeabi_d2iz                   
00002003  __aeabi_dadd                   
0000436d  __aeabi_dcmpeq                 
000043a9  __aeabi_dcmpge                 
000043bd  __aeabi_dcmpgt                 
00004395  __aeabi_dcmple                 
00004381  __aeabi_dcmplt                 
00002fa9  __aeabi_ddiv                   
0000338d  __aeabi_dmul                   
00001ff9  __aeabi_dsub                   
20200050  __aeabi_errno                  
00004069  __aeabi_errno_addr             
00004895  __aeabi_f2d                    
00004a05  __aeabi_f2iz                   
000037ef  __aeabi_fadd                   
00003e79  __aeabi_fdiv                   
00003d61  __aeabi_fmul                   
000037e5  __aeabi_fsub                   
00004b41  __aeabi_i2d                    
00004951  __aeabi_i2f                    
0000459d  __aeabi_idiv                   
00003efb  __aeabi_idiv0                  
0000459d  __aeabi_idivmod                
00003ff3  __aeabi_ldiv0                  
00004c79  __aeabi_llsl                   
00004c11  __aeabi_lmul                   
00004eb5  __aeabi_memcpy                 
00004eb5  __aeabi_memcpy4                
00004eb5  __aeabi_memcpy8                
00004dd1  __aeabi_memset                 
00004dd1  __aeabi_memset4                
00004dd1  __aeabi_memset8                
00004b99  __aeabi_ui2f                   
00004855  __aeabi_uidiv                  
00004855  __aeabi_uidivmod               
00004d61  __aeabi_uldivmod               
00004c79  __ashldi3                      
ffffffff  __binit__                      
00004235  __cmpdf2                       
00002fa9  __divdf3                       
00003e79  __divsf3                       
00004235  __eqdf2                        
00004895  __extendsfdf2                  
00004745  __fixdfsi                      
00004a05  __fixsfsi                      
00004b41  __floatsidf                    
00004951  __floatsisf                    
00004b99  __floatunsisf                  
00003ff5  __gedf2                        
00003ff5  __gtdf2                        
00004235  __ledf2                        
00004235  __ltdf2                        
UNDEFED   __mpu_init                     
0000338d  __muldf3                       
00004c11  __muldi3                       
000049c9  __muldsi3                      
00003d61  __mulsf3                       
00004235  __nedf2                        
20207e00  __stack                        
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
00001ff9  __subdf3                       
000037e5  __subsf3                       
00004071  __truncdfsf2                   
00003b95  __udivmoddi4                   
00004bc1  _c_int00_noargs                
UNDEFED   _system_post_cinit             
00004eed  _system_pre_init               
00004edb  abort                          
20200080  accel                          
000060d6  asc2_0806                      
00005ae6  asc2_1608                      
00000a91  asin                           
00000a91  asinl                          
00000df5  atan                           
0000218d  atan2                          
0000218d  atan2l                         
00000df5  atanl                          
000048d5  atoi                           
ffffffff  binit                          
000013b1  dmp_enable_feature             
00004cb5  dmp_load_motion_driver_firmware
00001e51  dmp_read_fifo                  
00004ded  dmp_register_android_orient_cb 
00004df9  dmp_register_tap_cb            
0000429d  dmp_set_fifo_rate              
000032a1  dmp_set_orientation            
00002c29  dmp_set_tap_thresh             
00004491  frexp                          
00004491  frexpl                         
20200086  gyro                           
000064c8  hw                             
00000000  interruptVectors               
0000370d  ldexp                          
0000370d  ldexpl                         
00004e05  led_blink_task                 
00004c97  main                           
00004c35  memccpy                        
00003c37  memcpy                         
000043cf  memset                         
202000a2  more                           
00004e11  mpu6050_clear_data_ready       
00004b6d  mpu6050_get_angles             
00003ded  mpu6050_i2c_sda_unlock         
00004e1d  mpu6050_is_data_ready          
00004a75  mpu6050_task                   
00003a45  mpu_configure_fifo             
00004aa9  mpu_get_accel_fsr              
00004c59  mpu_get_gyro_fsr               
00004cd1  mpu_get_sample_rate            
000031ad  mpu_init                       
00002729  mpu_load_firmware              
00001a91  mpu_lp_accel_mode              
00003af1  mpu_read_fifo_stream           
000025d9  mpu_reset_fifo                 
000030b5  mpu_set_bypass                 
000038bd  mpu_set_dmp_state              
000040e5  mpu_set_lpf                    
00003471  mpu_set_sample_rate            
00003631  mpu_set_sensors                
000041c9  mpu_write_mem                  
00004add  mspm0_delay_ms                 
00004d1d  mspm0_get_clock_ms             
00002485  mspm0_i2c_read                 
00002d5d  mspm0_i2c_write                
00003cd1  oled_task                      
2020008c  pitch                          
20200070  quat                           
00006470  reg                            
20200090  roll                           
0000370d  scalbn                         
0000370d  scalbnl                        
00004d75  scheduler_init                 
00004791  scheduler_run                  
20200094  sensor_timestamp               
202000a0  sensors                        
00004a3d  sprintf                        
00002315  sqrt                           
00002315  sqrtl                          
20200098  start_time                     
20200060  sys_tick                       
202000a3  task_num                       
00006448  test                           
00004dc1  wcslen                         
2020009c  yaw                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                           
-------   ----                           
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00000000  __TI_static_base__             
00000000  interruptVectors               
000000c1  __TI_printfi                   
00000200  __STACK_SIZE                   
00000a91  asin                           
00000a91  asinl                          
00000df5  atan                           
00000df5  atanl                          
000010ed  OLED_Init                      
000013b1  dmp_enable_feature             
00001881  Read_Quad                      
00001a91  mpu_lp_accel_mode              
00001e51  dmp_read_fifo                  
00001ff9  __aeabi_dsub                   
00001ff9  __subdf3                       
00002003  __adddf3                       
00002003  __aeabi_dadd                   
0000218b  ADC0_IRQHandler                
0000218b  ADC1_IRQHandler                
0000218b  AES_IRQHandler                 
0000218b  CANFD0_IRQHandler              
0000218b  DAC0_IRQHandler                
0000218b  DMA_IRQHandler                 
0000218b  Default_Handler                
0000218b  GROUP0_IRQHandler              
0000218b  HardFault_Handler              
0000218b  I2C0_IRQHandler                
0000218b  I2C1_IRQHandler                
0000218b  NMI_Handler                    
0000218b  PendSV_Handler                 
0000218b  RTC_IRQHandler                 
0000218b  SPI0_IRQHandler                
0000218b  SPI1_IRQHandler                
0000218b  SVC_Handler                    
0000218b  TIMA0_IRQHandler               
0000218b  TIMA1_IRQHandler               
0000218b  TIMG0_IRQHandler               
0000218b  TIMG12_IRQHandler              
0000218b  TIMG6_IRQHandler               
0000218b  TIMG7_IRQHandler               
0000218b  TIMG8_IRQHandler               
0000218b  UART0_IRQHandler               
0000218b  UART1_IRQHandler               
0000218b  UART2_IRQHandler               
0000218b  UART3_IRQHandler               
0000218d  atan2                          
0000218d  atan2l                         
00002315  sqrt                           
00002315  sqrtl                          
00002485  mspm0_i2c_read                 
000025d9  mpu_reset_fifo                 
00002729  mpu_load_firmware              
00002871  OLED_Clear                     
00002af1  OLED_WR_Byte                   
00002c29  dmp_set_tap_thresh             
00002d5d  mspm0_i2c_write                
00002fa9  __aeabi_ddiv                   
00002fa9  __divdf3                       
000030b5  mpu_set_bypass                 
000031ad  mpu_init                       
000032a1  dmp_set_orientation            
0000338d  __aeabi_dmul                   
0000338d  __muldf3                       
00003471  mpu_set_sample_rate            
00003555  OLED_ShowChar                  
00003631  mpu_set_sensors                
0000370d  ldexp                          
0000370d  ldexpl                         
0000370d  scalbn                         
0000370d  scalbnl                        
000037e5  __aeabi_fsub                   
000037e5  __subsf3                       
000037ef  __addsf3                       
000037ef  __aeabi_fadd                   
000038bd  mpu_set_dmp_state              
0000398d  MPU6050_Init                   
00003a45  mpu_configure_fifo             
00003af1  mpu_read_fifo_stream           
00003b95  __udivmoddi4                   
00003c37  memcpy                         
00003cd1  oled_task                      
00003d61  __aeabi_fmul                   
00003d61  __mulsf3                       
00003ded  mpu6050_i2c_sda_unlock         
00003e79  __aeabi_fdiv                   
00003e79  __divsf3                       
00003efb  __aeabi_idiv0                  
00003efd  __TI_decompress_lzss           
00003f79  OLED_ShowString                
00003ff3  __aeabi_ldiv0                  
00003ff5  __gedf2                        
00003ff5  __gtdf2                        
00004069  __aeabi_errno_addr             
00004071  __aeabi_d2f                    
00004071  __truncdfsf2                   
000040e5  mpu_set_lpf                    
00004159  SYSCFG_DL_GPIO_init            
000041c9  mpu_write_mem                  
00004235  __cmpdf2                       
00004235  __eqdf2                        
00004235  __ledf2                        
00004235  __ltdf2                        
00004235  __nedf2                        
0000429d  dmp_set_fifo_rate              
0000436d  __aeabi_dcmpeq                 
00004381  __aeabi_dcmplt                 
00004395  __aeabi_dcmple                 
000043a9  __aeabi_dcmpge                 
000043bd  __aeabi_dcmpgt                 
000043cf  memset                         
00004431  DL_I2C_fillControllerTXFIFO    
00004491  frexp                          
00004491  frexpl                         
000044ed  __TI_ltoa                      
0000459d  __aeabi_idiv                   
0000459d  __aeabi_idivmod                
000045f5  SYSCFG_DL_I2C_MPU6050_init     
00004649  SYSCFG_DL_I2C_OLED_init        
0000469d  SYSCFG_DL_SYSCTL_init          
00004745  __aeabi_d2iz                   
00004745  __fixdfsi                      
00004791  scheduler_run                  
000047d5  SYSCFG_DL_initPower            
00004815  SysTick_Init                   
00004855  __aeabi_uidiv                  
00004855  __aeabi_uidivmod               
00004895  __aeabi_f2d                    
00004895  __extendsfdf2                  
000048d5  atoi                           
00004915  DL_I2C_flushControllerTXFIFO   
00004951  __aeabi_i2f                    
00004951  __floatsisf                    
0000498d  __TI_auto_init_nobinit_nopinit 
000049c9  __muldsi3                      
00004a05  __aeabi_f2iz                   
00004a05  __fixsfsi                      
00004a3d  sprintf                        
00004a75  mpu6050_task                   
00004aa9  mpu_get_accel_fsr              
00004add  mspm0_delay_ms                 
00004b41  __aeabi_i2d                    
00004b41  __floatsidf                    
00004b6d  mpu6050_get_angles             
00004b99  __aeabi_ui2f                   
00004b99  __floatunsisf                  
00004bc1  _c_int00_noargs                
00004be9  DL_I2C_setClockConfig          
00004c11  __aeabi_lmul                   
00004c11  __muldi3                       
00004c35  memccpy                        
00004c59  mpu_get_gyro_fsr               
00004c79  __aeabi_llsl                   
00004c79  __ashldi3                      
00004c97  main                           
00004cb5  dmp_load_motion_driver_firmware
00004cd1  mpu_get_sample_rate            
00004ced  SYSCFG_DL_init                 
00004d1d  mspm0_get_clock_ms             
00004d35  __TI_zero_init_nomemset        
00004d4d  GROUP1_IRQHandler              
00004d61  __aeabi_uldivmod               
00004d75  scheduler_init                 
00004d9d  __TI_decompress_none           
00004db1  SysTick_Handler                
00004dc1  wcslen                         
00004dd1  __aeabi_memset                 
00004dd1  __aeabi_memset4                
00004dd1  __aeabi_memset8                
00004ded  dmp_register_android_orient_cb 
00004df9  dmp_register_tap_cb            
00004e05  led_blink_task                 
00004e11  mpu6050_clear_data_ready       
00004e1d  mpu6050_is_data_ready          
00004e29  DL_Common_delayCycles          
00004eb5  __aeabi_memcpy                 
00004eb5  __aeabi_memcpy4                
00004eb5  __aeabi_memcpy8                
00004edb  abort                          
00004ee0  C$$EXIT                        
00004ee1  HOSTexit                       
00004ee9  Reset_Handler                  
00004eed  _system_pre_init               
00005ae6  asc2_1608                      
000060d6  asc2_0806                      
00006300  __aeabi_ctype_table_           
00006300  __aeabi_ctype_table_C          
00006448  test                           
00006470  reg                            
000064c8  hw                             
0000652c  __TI_Handler_Table_Base        
00006538  __TI_Handler_Table_Limit       
00006540  __TI_CINIT_Base                
00006550  __TI_CINIT_Limit               
00006550  __TI_CINIT_Warm                
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
20200050  __aeabi_errno                  
20200060  sys_tick                       
20200070  quat                           
20200080  accel                          
20200086  gyro                           
2020008c  pitch                          
20200090  roll                           
20200094  sensor_timestamp               
20200098  start_time                     
2020009c  yaw                            
202000a0  sensors                        
202000a2  more                           
202000a3  task_num                       
20207e00  __stack                        
20208000  __STACK_END                    
ffffffff  __TI_pprof_out_hndl            
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
ffffffff  __binit__                      
ffffffff  binit                          
UNDEFED   __mpu_init                     
UNDEFED   _system_post_cinit             

[229 symbols]

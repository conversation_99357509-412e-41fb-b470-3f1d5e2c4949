******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jun 26 23:15:52 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004281


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005b88  0001a478  R  X
  SRAM                  20200000   00008000  000002a6  00007d5a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005b88   00005b88    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004480   00004480    r-x .text
  00004540    00004540    000015f0   000015f0    r-- .rodata
  00005b30    00005b30    00000058   00000058    r-- .cinit
20200000    20200000    000000a8   00000000    rw-
  20200000    20200000    00000072   00000000    rw- .data
  20200074    20200074    00000034   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004480     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000002c4     oled_hardware_i2c.o (.text.OLED_Init)
                  00000d54    000002b0     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001004    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001224    000001e4     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00001408    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000015e4    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  0000178c    000001a4     mspm0_i2c.o (.text.mspm0_i2c_read)
                  00001930    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001ac2    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001ac4    00000188     mpu6050.o (.text.Read_Quad)
                  00001c4c    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  00001d9c    00000148     inv_mpu.o (.text.mpu_load_firmware)
                  00001ee4    00000144     oled_hardware_i2c.o (.text.OLED_Clear)
                  00002028    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002164    00000138     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  0000229c    00000134     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  000023d0    00000130     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00002500    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002620    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000272c    000000f8     inv_mpu.o (.text.mpu_set_bypass)
                  00002824    000000f4     inv_mpu.o (.text.mpu_init)
                  00002918    000000ec     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002a04    000000e8     oled_app.o (.text.oled_task)
                  00002aec    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002bd0    000000e4     inv_mpu.o (.text.mpu_set_sample_rate)
                  00002cb4    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00002d90    000000dc     inv_mpu.o (.text.mpu_set_sensors)
                  00002e6c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002f44    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000301c    000000d0     inv_mpu.o (.text.mpu_set_dmp_state)
                  000030ec    000000ac     inv_mpu.o (.text.mpu_configure_fifo)
                  00003198    000000a4     mpu6050.o (.text.MPU6050_Init)
                  0000323c    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  000032e0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003382    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000341c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000034a8    0000008c     mspm0_i2c.o (.text.mpu6050_i2c_sda_unlock)
                  00003534    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000035b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000035b8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003634    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  000036ae    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000036b0    00000074                            : comparedf2.c.obj (.text.__gedf2)
                  00003724    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00003730    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000037a4    00000074     inv_mpu.o (.text.mpu_set_lpf)
                  00003818    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00003888    0000006c     inv_mpu.o (.text.mpu_write_mem)
                  000038f4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000395c    00000068     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000039c4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003a2a    00000002     mpu6050.o (.text.android_orient_cb)
                  00003a2c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003a8e    00000062     libc.a : memset16.S.obj (.text:memset)
                  00003af0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003b4e    00000002     mpu6050.o (.text.tap_cb)
                  00003b50    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003bac    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00003c04    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003c5c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003cb2    00000002     --HOLE-- [fill = 0]
                  00003cb4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00003d08    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003d5c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003db0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003e02    00000002     --HOLE-- [fill = 0]
                  00003e04    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003e4e    00000002     --HOLE-- [fill = 0]
                  00003e50    00000044     scheduler.o (.text.scheduler_run)
                  00003e94    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003ed4    00000040     clock.o (.text.SysTick_Init)
                  00003f14    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003f54    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003f94    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003fd4    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004010    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000404c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004088    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000040c2    00000002     --HOLE-- [fill = 0]
                  000040c4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000040fc    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004134    00000034     mpu6050_app.o (.text.mpu6050_task)
                  00004168    00000034     inv_mpu.o (.text.mpu_get_accel_fsr)
                  0000419c    00000034     clock.o (.text.mspm0_delay_ms)
                  000041d0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004200    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000422c    0000002c     mpu6050_app.o (.text.mpu6050_get_angles)
                  00004258    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00004280    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000042a8    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000042ce    00000002     --HOLE-- [fill = 0]
                  000042d0    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000042f4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004316    00000002     --HOLE-- [fill = 0]
                  00004318    00000020     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00004338    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004356    0000001e     empty.o (.text.main)
                  00004374    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00004390    0000001c     inv_mpu.o (.text.mpu_get_sample_rate)
                  000043ac    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000043c4    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000043dc    00000018     clock.o (.text.mspm0_get_clock_ms)
                  000043f4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000440a    00000002     --HOLE-- [fill = 0]
                  0000440c    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  00004420    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004434    00000014     scheduler.o (.text.scheduler_init)
                  00004448    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000445c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000446e    00000002     --HOLE-- [fill = 0]
                  00004470    00000010     interrupt.o (.text.SysTick_Handler)
                  00004480    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004490    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000449e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000044ac    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000044b8    0000000c     empty.o (.text.led_blink_task)
                  000044c4    0000000c     mpu6050_app.o (.text.mpu6050_clear_data_ready)
                  000044d0    0000000c     mpu6050_app.o (.text.mpu6050_is_data_ready)
                  000044dc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000044e6    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000044f0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004500    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000450a    0000000a            : sprintf.c.obj (.text._outc)
                  00004514    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000451c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004524    00000006     libc.a : exit.c.obj (.text:abort)
                  0000452a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000452e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004532    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00004536    0000000a     --HOLE-- [fill = 0]

.cinit     0    00005b30    00000058     
                  00005b30    0000002d     (.cinit..data.load) [load image, compression = lzss]
                  00005b5d    00000003     --HOLE-- [fill = 0]
                  00005b60    0000000c     (__TI_handler_table)
                  00005b6c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005b74    00000010     (__TI_cinit_table)
                  00005b84    00000004     --HOLE-- [fill = 0]

.rodata    0    00004540    000015f0     
                  00004540    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00005136    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005726    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  0000594e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005950    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005a51    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.accel_axes)
                  00005a54    00000028     inv_mpu.o (.rodata.test)
                  00005a7c    0000001e     inv_mpu.o (.rodata.reg)
                  00005a9a    00000018     inv_mpu.o (.rodata.cst8)
                  00005ab2    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005ac3    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005ad4    00000010     oled_app.o (.rodata.str1.4119433759365661616.1)
                  00005ae4    0000000c     inv_mpu.o (.rodata.hw)
                  00005af0    0000000c     oled_app.o (.rodata.str1.10123847253219111340.1)
                  00005afc    0000000c     oled_app.o (.rodata.str1.17288060079586621852.1)
                  00005b08    0000000c     oled_app.o (.rodata.str1.865768355259620247.1)
                  00005b14    0000000a     oled_app.o (.rodata.str1.3320576987535119861.1)
                  00005b1e    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.gyro_axes)
                  00005b21    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005b23    0000000d     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000072     UNINITIALIZED
                  20200000    0000002c     inv_mpu.o (.data.st)
                  2020002c    00000024     scheduler.o (.data.scheduler_task)
                  20200050    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200054    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200058    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  2020005c    00000004     mpu6050_app.o (.data.mpu6050_task.last_update_time)
                  20200060    00000004     oled_app.o (.data.oled_task.counter)
                  20200064    00000004     clock.o (.data.sys_tick)
                  20200068    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.2)
                  2020006a    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)
                  2020006b    00000001     mpu6050_app.o (.data.mpu6050_data_ready)
                  2020006c    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  2020006e    00000002     --HOLE--
                  20200070    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.4)

.bss       0    20200074    00000034     UNINITIALIZED
                  20200074    00000010     (.common:quat)
                  20200084    00000006     (.common:accel)
                  2020008a    00000006     (.common:gyro)
                  20200090    00000004     (.common:pitch)
                  20200094    00000004     (.common:roll)
                  20200098    00000004     (.common:sensor_timestamp)
                  2020009c    00000004     (.common:start_time)
                  202000a0    00000004     (.common:yaw)
                  202000a4    00000002     (.common:sensors)
                  202000a6    00000001     (.common:more)
                  202000a7    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             452     4         0      
       startup_mspm0g350x_ticlang.o   6       192       0      
       scheduler.o                    88      0         37     
       empty.o                        42      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         588     196       37     
                                                               
    .\APP\
       oled_app.o                     232     62        4      
       mpu6050_app.o                  120     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         352     62        9      
                                                               
    .\BSP\MPU6050\
       inv_mpu_dmp_motion_driver.o    1812    3068      15     
       inv_mpu.o                      2968    106       44     
       mspm0_i2c.o                    864     0         0      
       mpu6050.o                      560     0         47     
    +--+------------------------------+-------+---------+---------+
       Total:                         6204    3174      106    
                                                               
    .\BSP\OLED\
       oled_hardware_i2c.o            1686    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1686    2072      0      
                                                               
    .\System\
       clock.o                        140     0         8      
       interrupt.o                    36      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         176     0         8      
                                                               
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       192     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         202     0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5644    291       4      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2654    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       81        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17510   5876      676    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005b74 records: 2, size/record: 8, table size: 16
	.data: load addr=00005b30, load size=0000002d bytes, run addr=20200000, run size=00000072 bytes, compression=lzss
	.bss: load addr=00005b6c, load size=00000008 bytes, run addr=20200074, run size=00000034 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005b60 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001931     000044f0     000044ee   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                           
-------   ----                           
00001ac3  ADC0_IRQHandler                
00001ac3  ADC1_IRQHandler                
00001ac3  AES_IRQHandler                 
0000452a  C$$EXIT                        
00001ac3  CANFD0_IRQHandler              
00001ac3  DAC0_IRQHandler                
000044dd  DL_Common_delayCycles          
00003af1  DL_I2C_fillControllerTXFIFO    
00003fd5  DL_I2C_flushControllerTXFIFO   
000042a9  DL_I2C_setClockConfig          
00001ac3  DMA_IRQHandler                 
00001ac3  Default_Handler                
00001ac3  GROUP0_IRQHandler              
0000440d  GROUP1_IRQHandler              
0000452b  HOSTexit                       
00001ac3  HardFault_Handler              
00001ac3  I2C0_IRQHandler                
00001ac3  I2C1_IRQHandler                
00003199  MPU6050_Init                   
00001ac3  NMI_Handler                    
00001ee5  OLED_Clear                     
00000a91  OLED_Init                      
00002cb5  OLED_ShowChar                  
00003635  OLED_ShowString                
00002165  OLED_WR_Byte                   
00001ac3  PendSV_Handler                 
00001ac3  RTC_IRQHandler                 
00001ac5  Read_Quad                      
0000452f  Reset_Handler                  
00001ac3  SPI0_IRQHandler                
00001ac3  SPI1_IRQHandler                
00001ac3  SVC_Handler                    
00003819  SYSCFG_DL_GPIO_init            
00003cb5  SYSCFG_DL_I2C_MPU6050_init     
00003d09  SYSCFG_DL_I2C_OLED_init        
00003d5d  SYSCFG_DL_SYSCTL_init          
000043ad  SYSCFG_DL_init                 
00003e95  SYSCFG_DL_initPower            
00004471  SysTick_Handler                
00003ed5  SysTick_Init                   
00001ac3  TIMA0_IRQHandler               
00001ac3  TIMA1_IRQHandler               
00001ac3  TIMG0_IRQHandler               
00001ac3  TIMG12_IRQHandler              
00001ac3  TIMG6_IRQHandler               
00001ac3  TIMG7_IRQHandler               
00001ac3  TIMG8_IRQHandler               
00001ac3  UART0_IRQHandler               
00001ac3  UART1_IRQHandler               
00001ac3  UART2_IRQHandler               
00001ac3  UART3_IRQHandler               
20208000  __STACK_END                    
00000200  __STACK_SIZE                   
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00005b74  __TI_CINIT_Base                
00005b84  __TI_CINIT_Limit               
00005b84  __TI_CINIT_Warm                
00005b60  __TI_Handler_Table_Base        
00005b6c  __TI_Handler_Table_Limit       
0000404d  __TI_auto_init_nobinit_nopinit 
000035b9  __TI_decompress_lzss           
0000445d  __TI_decompress_none           
00003bad  __TI_ltoa                      
ffffffff  __TI_pprof_out_hndl            
000000c1  __TI_printfi                   
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
00000000  __TI_static_base__             
000043f5  __TI_zero_init_nomemset        
0000193b  __adddf3                       
00002f4f  __addsf3                       
00005950  __aeabi_ctype_table_           
00005950  __aeabi_ctype_table_C          
00003731  __aeabi_d2f                    
00003e05  __aeabi_d2iz                   
0000193b  __aeabi_dadd                   
00003a2d  __aeabi_dcmpeq                 
00003a69  __aeabi_dcmpge                 
00003a7d  __aeabi_dcmpgt                 
00003a55  __aeabi_dcmple                 
00003a41  __aeabi_dcmplt                 
00002621  __aeabi_ddiv                   
00002aed  __aeabi_dmul                   
00001931  __aeabi_dsub                   
20200050  __aeabi_errno                  
00004515  __aeabi_errno_addr             
00003f55  __aeabi_f2d                    
000040c5  __aeabi_f2iz                   
00002f4f  __aeabi_fadd                   
00003535  __aeabi_fdiv                   
0000341d  __aeabi_fmul                   
00002f45  __aeabi_fsub                   
00004201  __aeabi_i2d                    
00004011  __aeabi_i2f                    
00003c5d  __aeabi_idiv                   
000035b7  __aeabi_idiv0                  
00003c5d  __aeabi_idivmod                
000036af  __aeabi_ldiv0                  
00004339  __aeabi_llsl                   
000042d1  __aeabi_lmul                   
0000451d  __aeabi_memcpy                 
0000451d  __aeabi_memcpy4                
0000451d  __aeabi_memcpy8                
00004491  __aeabi_memset                 
00004491  __aeabi_memset4                
00004491  __aeabi_memset8                
00004259  __aeabi_ui2f                   
00003f15  __aeabi_uidiv                  
00003f15  __aeabi_uidivmod               
00004421  __aeabi_uldivmod               
00004339  __ashldi3                      
ffffffff  __binit__                      
000038f5  __cmpdf2                       
00002621  __divdf3                       
00003535  __divsf3                       
000038f5  __eqdf2                        
00003f55  __extendsfdf2                  
00003e05  __fixdfsi                      
000040c5  __fixsfsi                      
00004201  __floatsidf                    
00004011  __floatsisf                    
00004259  __floatunsisf                  
000036b1  __gedf2                        
000036b1  __gtdf2                        
000038f5  __ledf2                        
000038f5  __ltdf2                        
UNDEFED   __mpu_init                     
00002aed  __muldf3                       
000042d1  __muldi3                       
00004089  __muldsi3                      
0000341d  __mulsf3                       
000038f5  __nedf2                        
20207e00  __stack                        
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
00001931  __subdf3                       
00002f45  __subsf3                       
00003731  __truncdfsf2                   
000032e1  __udivmoddi4                   
00004281  _c_int00_noargs                
UNDEFED   _system_post_cinit             
00004533  _system_pre_init               
00004525  abort                          
20200084  accel                          
00005726  asc2_0806                      
00005136  asc2_1608                      
00003f95  atoi                           
ffffffff  binit                          
00000d55  dmp_enable_feature             
00004375  dmp_load_motion_driver_firmware
000015e5  dmp_read_fifo                  
00003725  dmp_register_android_orient_cb 
000044ad  dmp_register_tap_cb            
0000395d  dmp_set_fifo_rate              
00002919  dmp_set_orientation            
0000229d  dmp_set_tap_thresh             
00003b51  frexp                          
00003b51  frexpl                         
2020008a  gyro                           
00005ae4  hw                             
00000000  interruptVectors               
00002e6d  ldexp                          
00002e6d  ldexpl                         
000044b9  led_blink_task                 
00004357  main                           
000042f5  memccpy                        
00003383  memcpy                         
00003a8f  memset                         
202000a6  more                           
000044c5  mpu6050_clear_data_ready       
0000422d  mpu6050_get_angles             
000034a9  mpu6050_i2c_sda_unlock         
000044d1  mpu6050_is_data_ready          
00004135  mpu6050_task                   
000030ed  mpu_configure_fifo             
00004169  mpu_get_accel_fsr              
00004319  mpu_get_gyro_fsr               
00004391  mpu_get_sample_rate            
00002825  mpu_init                       
00001d9d  mpu_load_firmware              
00001225  mpu_lp_accel_mode              
0000323d  mpu_read_fifo_stream           
00001c4d  mpu_reset_fifo                 
0000272d  mpu_set_bypass                 
0000301d  mpu_set_dmp_state              
000037a5  mpu_set_lpf                    
00002bd1  mpu_set_sample_rate            
00002d91  mpu_set_sensors                
00003889  mpu_write_mem                  
0000419d  mspm0_delay_ms                 
000043dd  mspm0_get_clock_ms             
0000178d  mspm0_i2c_read                 
000023d1  mspm0_i2c_write                
00002a05  oled_task                      
20200090  pitch                          
20200074  quat                           
00005a7c  reg                            
20200094  roll                           
00002e6d  scalbn                         
00002e6d  scalbnl                        
00004435  scheduler_init                 
00003e51  scheduler_run                  
20200098  sensor_timestamp               
202000a4  sensors                        
000040fd  sprintf                        
2020009c  start_time                     
20200064  sys_tick                       
202000a7  task_num                       
00005a54  test                           
00004481  wcslen                         
202000a0  yaw                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                           
-------   ----                           
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00000000  __TI_static_base__             
00000000  interruptVectors               
000000c1  __TI_printfi                   
00000200  __STACK_SIZE                   
00000a91  OLED_Init                      
00000d55  dmp_enable_feature             
00001225  mpu_lp_accel_mode              
000015e5  dmp_read_fifo                  
0000178d  mspm0_i2c_read                 
00001931  __aeabi_dsub                   
00001931  __subdf3                       
0000193b  __adddf3                       
0000193b  __aeabi_dadd                   
00001ac3  ADC0_IRQHandler                
00001ac3  ADC1_IRQHandler                
00001ac3  AES_IRQHandler                 
00001ac3  CANFD0_IRQHandler              
00001ac3  DAC0_IRQHandler                
00001ac3  DMA_IRQHandler                 
00001ac3  Default_Handler                
00001ac3  GROUP0_IRQHandler              
00001ac3  HardFault_Handler              
00001ac3  I2C0_IRQHandler                
00001ac3  I2C1_IRQHandler                
00001ac3  NMI_Handler                    
00001ac3  PendSV_Handler                 
00001ac3  RTC_IRQHandler                 
00001ac3  SPI0_IRQHandler                
00001ac3  SPI1_IRQHandler                
00001ac3  SVC_Handler                    
00001ac3  TIMA0_IRQHandler               
00001ac3  TIMA1_IRQHandler               
00001ac3  TIMG0_IRQHandler               
00001ac3  TIMG12_IRQHandler              
00001ac3  TIMG6_IRQHandler               
00001ac3  TIMG7_IRQHandler               
00001ac3  TIMG8_IRQHandler               
00001ac3  UART0_IRQHandler               
00001ac3  UART1_IRQHandler               
00001ac3  UART2_IRQHandler               
00001ac3  UART3_IRQHandler               
00001ac5  Read_Quad                      
00001c4d  mpu_reset_fifo                 
00001d9d  mpu_load_firmware              
00001ee5  OLED_Clear                     
00002165  OLED_WR_Byte                   
0000229d  dmp_set_tap_thresh             
000023d1  mspm0_i2c_write                
00002621  __aeabi_ddiv                   
00002621  __divdf3                       
0000272d  mpu_set_bypass                 
00002825  mpu_init                       
00002919  dmp_set_orientation            
00002a05  oled_task                      
00002aed  __aeabi_dmul                   
00002aed  __muldf3                       
00002bd1  mpu_set_sample_rate            
00002cb5  OLED_ShowChar                  
00002d91  mpu_set_sensors                
00002e6d  ldexp                          
00002e6d  ldexpl                         
00002e6d  scalbn                         
00002e6d  scalbnl                        
00002f45  __aeabi_fsub                   
00002f45  __subsf3                       
00002f4f  __addsf3                       
00002f4f  __aeabi_fadd                   
0000301d  mpu_set_dmp_state              
000030ed  mpu_configure_fifo             
00003199  MPU6050_Init                   
0000323d  mpu_read_fifo_stream           
000032e1  __udivmoddi4                   
00003383  memcpy                         
0000341d  __aeabi_fmul                   
0000341d  __mulsf3                       
000034a9  mpu6050_i2c_sda_unlock         
00003535  __aeabi_fdiv                   
00003535  __divsf3                       
000035b7  __aeabi_idiv0                  
000035b9  __TI_decompress_lzss           
00003635  OLED_ShowString                
000036af  __aeabi_ldiv0                  
000036b1  __gedf2                        
000036b1  __gtdf2                        
00003725  dmp_register_android_orient_cb 
00003731  __aeabi_d2f                    
00003731  __truncdfsf2                   
000037a5  mpu_set_lpf                    
00003819  SYSCFG_DL_GPIO_init            
00003889  mpu_write_mem                  
000038f5  __cmpdf2                       
000038f5  __eqdf2                        
000038f5  __ledf2                        
000038f5  __ltdf2                        
000038f5  __nedf2                        
0000395d  dmp_set_fifo_rate              
00003a2d  __aeabi_dcmpeq                 
00003a41  __aeabi_dcmplt                 
00003a55  __aeabi_dcmple                 
00003a69  __aeabi_dcmpge                 
00003a7d  __aeabi_dcmpgt                 
00003a8f  memset                         
00003af1  DL_I2C_fillControllerTXFIFO    
00003b51  frexp                          
00003b51  frexpl                         
00003bad  __TI_ltoa                      
00003c5d  __aeabi_idiv                   
00003c5d  __aeabi_idivmod                
00003cb5  SYSCFG_DL_I2C_MPU6050_init     
00003d09  SYSCFG_DL_I2C_OLED_init        
00003d5d  SYSCFG_DL_SYSCTL_init          
00003e05  __aeabi_d2iz                   
00003e05  __fixdfsi                      
00003e51  scheduler_run                  
00003e95  SYSCFG_DL_initPower            
00003ed5  SysTick_Init                   
00003f15  __aeabi_uidiv                  
00003f15  __aeabi_uidivmod               
00003f55  __aeabi_f2d                    
00003f55  __extendsfdf2                  
00003f95  atoi                           
00003fd5  DL_I2C_flushControllerTXFIFO   
00004011  __aeabi_i2f                    
00004011  __floatsisf                    
0000404d  __TI_auto_init_nobinit_nopinit 
00004089  __muldsi3                      
000040c5  __aeabi_f2iz                   
000040c5  __fixsfsi                      
000040fd  sprintf                        
00004135  mpu6050_task                   
00004169  mpu_get_accel_fsr              
0000419d  mspm0_delay_ms                 
00004201  __aeabi_i2d                    
00004201  __floatsidf                    
0000422d  mpu6050_get_angles             
00004259  __aeabi_ui2f                   
00004259  __floatunsisf                  
00004281  _c_int00_noargs                
000042a9  DL_I2C_setClockConfig          
000042d1  __aeabi_lmul                   
000042d1  __muldi3                       
000042f5  memccpy                        
00004319  mpu_get_gyro_fsr               
00004339  __aeabi_llsl                   
00004339  __ashldi3                      
00004357  main                           
00004375  dmp_load_motion_driver_firmware
00004391  mpu_get_sample_rate            
000043ad  SYSCFG_DL_init                 
000043dd  mspm0_get_clock_ms             
000043f5  __TI_zero_init_nomemset        
0000440d  GROUP1_IRQHandler              
00004421  __aeabi_uldivmod               
00004435  scheduler_init                 
0000445d  __TI_decompress_none           
00004471  SysTick_Handler                
00004481  wcslen                         
00004491  __aeabi_memset                 
00004491  __aeabi_memset4                
00004491  __aeabi_memset8                
000044ad  dmp_register_tap_cb            
000044b9  led_blink_task                 
000044c5  mpu6050_clear_data_ready       
000044d1  mpu6050_is_data_ready          
000044dd  DL_Common_delayCycles          
00004515  __aeabi_errno_addr             
0000451d  __aeabi_memcpy                 
0000451d  __aeabi_memcpy4                
0000451d  __aeabi_memcpy8                
00004525  abort                          
0000452a  C$$EXIT                        
0000452b  HOSTexit                       
0000452f  Reset_Handler                  
00004533  _system_pre_init               
00005136  asc2_1608                      
00005726  asc2_0806                      
00005950  __aeabi_ctype_table_           
00005950  __aeabi_ctype_table_C          
00005a54  test                           
00005a7c  reg                            
00005ae4  hw                             
00005b60  __TI_Handler_Table_Base        
00005b6c  __TI_Handler_Table_Limit       
00005b74  __TI_CINIT_Base                
00005b84  __TI_CINIT_Limit               
00005b84  __TI_CINIT_Warm                
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
20200050  __aeabi_errno                  
20200064  sys_tick                       
20200074  quat                           
20200084  accel                          
2020008a  gyro                           
20200090  pitch                          
20200094  roll                           
20200098  sensor_timestamp               
2020009c  start_time                     
202000a0  yaw                            
202000a4  sensors                        
202000a6  more                           
202000a7  task_num                       
20207e00  __stack                        
20208000  __STACK_END                    
ffffffff  __TI_pprof_out_hndl            
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
ffffffff  __binit__                      
ffffffff  binit                          
UNDEFED   __mpu_init                     
UNDEFED   _system_post_cinit             

[221 symbols]

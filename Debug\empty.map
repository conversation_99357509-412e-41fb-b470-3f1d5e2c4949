******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jun 26 23:52:48 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000042c1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005be8  0001a418  R  X
  SRAM                  20200000   00008000  000002a6  00007d5a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005be8   00005be8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000044d0   000044d0    r-x .text
  00004590    00004590    00001600   00001600    r-- .rodata
  00005b90    00005b90    00000058   00000058    r-- .cinit
20200000    20200000    000000a8   00000000    rw-
  20200000    20200000    00000072   00000000    rw- .data
  20200074    20200074    00000034   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000044d0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000002c4     oled_hardware_i2c.o (.text.OLED_Init)
                  00000d54    000002b0     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001004    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001224    000001e4     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00001408    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000015e4    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  0000178c    000001a4     mspm0_i2c.o (.text.mspm0_i2c_read)
                  00001930    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001ac2    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001ac4    00000188     mpu6050.o (.text.Read_Quad)
                  00001c4c    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  00001d9c    00000148     inv_mpu.o (.text.mpu_load_firmware)
                  00001ee4    00000144     oled_hardware_i2c.o (.text.OLED_Clear)
                  00002028    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002164    00000138     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  0000229c    00000134     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  000023d0    00000130     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00002500    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002620    00000118     oled_app.o (.text.oled_task)
                  00002738    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002844    000000f8     inv_mpu.o (.text.mpu_set_bypass)
                  0000293c    000000f4     inv_mpu.o (.text.mpu_init)
                  00002a30    000000ec     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002b1c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002c00    000000e4     inv_mpu.o (.text.mpu_set_sample_rate)
                  00002ce4    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00002dc0    000000dc     inv_mpu.o (.text.mpu_set_sensors)
                  00002e9c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002f74    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000304c    000000d0     inv_mpu.o (.text.mpu_set_dmp_state)
                  0000311c    000000ac     inv_mpu.o (.text.mpu_configure_fifo)
                  000031c8    000000a4     mpu6050.o (.text.MPU6050_Init)
                  0000326c    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003310    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000033b2    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000344c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000034d8    0000008c     mspm0_i2c.o (.text.mpu6050_i2c_sda_unlock)
                  00003564    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000035e6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000035e8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003664    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  000036de    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000036e0    00000074                            : comparedf2.c.obj (.text.__gedf2)
                  00003754    0000000c     clock.o (.text.SystemTime_GetMs)
                  00003760    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000037d4    00000074     inv_mpu.o (.text.mpu_set_lpf)
                  00003848    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000038b8    0000006c     inv_mpu.o (.text.mpu_write_mem)
                  00003924    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000398c    00000068     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000039f4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003a5a    00000002     mpu6050.o (.text.android_orient_cb)
                  00003a5c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003abe    00000062     libc.a : memset16.S.obj (.text:memset)
                  00003b20    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003b7e    00000002     mpu6050.o (.text.tap_cb)
                  00003b80    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003bdc    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00003c34    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003c8c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003ce2    00000002     --HOLE-- [fill = 0]
                  00003ce4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00003d38    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003d8c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003de0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003e32    00000002     --HOLE-- [fill = 0]
                  00003e34    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003e7e    00000002     --HOLE-- [fill = 0]
                  00003e80    00000044     mpu6050_app.o (.text.mpu6050_task)
                  00003ec4    00000044     scheduler.o (.text.scheduler_run)
                  00003f08    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003f48    00000040     clock.o (.text.SysTick_Init)
                  00003f88    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003fc8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004008    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004048    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004084    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000040c0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000040fc    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00004136    00000002     --HOLE-- [fill = 0]
                  00004138    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00004170    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  000041a8    00000034     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000041dc    00000034     clock.o (.text.mspm0_delay_ms)
                  00004210    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004240    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000426c    0000002c     mpu6050_app.o (.text.mpu6050_get_angles)
                  00004298    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000042c0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000042e8    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000430e    00000002     --HOLE-- [fill = 0]
                  00004310    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004334    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004356    00000002     --HOLE-- [fill = 0]
                  00004358    00000020     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00004378    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004396    0000001e     empty.o (.text.main)
                  000043b4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000043d0    0000001c     inv_mpu.o (.text.mpu_get_sample_rate)
                  000043ec    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004404    00000018     libc.a : sprintf.c.obj (.text._outs)
                  0000441c    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00004434    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000444a    00000002     --HOLE-- [fill = 0]
                  0000444c    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  00004460    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004474    00000014     scheduler.o (.text.scheduler_init)
                  00004488    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000449c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000044ae    00000002     --HOLE-- [fill = 0]
                  000044b0    00000010     interrupt.o (.text.SysTick_Handler)
                  000044c0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000044d0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000044de    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000044ec    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  000044f8    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00004504    0000000c     empty.o (.text.led_blink_task)
                  00004510    0000000c     mpu6050_app.o (.text.mpu6050_clear_data_ready)
                  0000451c    0000000c     mpu6050_app.o (.text.mpu6050_is_data_ready)
                  00004528    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004532    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000453c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000454c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004556    0000000a            : sprintf.c.obj (.text._outc)
                  00004560    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004568    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004570    00000006     libc.a : exit.c.obj (.text:abort)
                  00004576    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000457a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000457e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00004582    0000000e     --HOLE-- [fill = 0]

.cinit     0    00005b90    00000058     
                  00005b90    0000002d     (.cinit..data.load) [load image, compression = lzss]
                  00005bbd    00000003     --HOLE-- [fill = 0]
                  00005bc0    0000000c     (__TI_handler_table)
                  00005bcc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005bd4    00000010     (__TI_cinit_table)
                  00005be4    00000004     --HOLE-- [fill = 0]

.rodata    0    00004590    00001600     
                  00004590    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00005186    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005776    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  0000599e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000059a0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005aa1    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.accel_axes)
                  00005aa4    00000028     inv_mpu.o (.rodata.test)
                  00005acc    0000001e     inv_mpu.o (.rodata.reg)
                  00005aea    00000018     inv_mpu.o (.rodata.cst8)
                  00005b02    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005b13    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005b24    0000000f     oled_app.o (.rodata.str1.4119433759365661616.1)
                  00005b33    0000000e     oled_app.o (.rodata.str1.11222535813127866853.1)
                  00005b41    0000000d     oled_app.o (.rodata.str1.16484802703283353337.1)
                  00005b4e    0000000c     inv_mpu.o (.rodata.hw)
                  00005b5a    0000000c     oled_app.o (.rodata.str1.10123847253219111340.1)
                  00005b66    0000000c     oled_app.o (.rodata.str1.17288060079586621852.1)
                  00005b72    0000000c     oled_app.o (.rodata.str1.865768355259620247.1)
                  00005b7e    0000000a     oled_app.o (.rodata.str1.3320576987535119861.1)
                  00005b88    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.gyro_axes)
                  00005b8b    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005b8d    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000072     UNINITIALIZED
                  20200000    0000002c     inv_mpu.o (.data.st)
                  2020002c    00000024     scheduler.o (.data.scheduler_task)
                  20200050    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200054    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200058    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  2020005c    00000004     mpu6050_app.o (.data.mpu6050_task.last_update_time)
                  20200060    00000004     oled_app.o (.data.oled_task.counter)
                  20200064    00000004     clock.o (.data.sys_tick)
                  20200068    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.2)
                  2020006a    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)
                  2020006b    00000001     mpu6050_app.o (.data.mpu6050_data_ready)
                  2020006c    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  2020006e    00000001     mpu6050_app.o (.data.mpu6050_task.init_attempted)
                  2020006f    00000001     --HOLE--
                  20200070    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.4)

.bss       0    20200074    00000034     UNINITIALIZED
                  20200074    00000010     (.common:quat)
                  20200084    00000006     (.common:accel)
                  2020008a    00000006     (.common:gyro)
                  20200090    00000004     (.common:pitch)
                  20200094    00000004     (.common:roll)
                  20200098    00000004     (.common:sensor_timestamp)
                  2020009c    00000004     (.common:start_time)
                  202000a0    00000004     (.common:yaw)
                  202000a4    00000002     (.common:sensors)
                  202000a6    00000001     (.common:more)
                  202000a7    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             452     4         0      
       startup_mspm0g350x_ticlang.o   6       192       0      
       scheduler.o                    88      0         37     
       empty.o                        42      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         588     196       37     
                                                               
    .\APP\
       oled_app.o                     280     88        4      
       mpu6050_app.o                  136     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         416     88        10     
                                                               
    .\BSP\MPU6050\
       inv_mpu_dmp_motion_driver.o    1812    3068      15     
       inv_mpu.o                      2968    106       44     
       mspm0_i2c.o                    864     0         0      
       mpu6050.o                      560     0         47     
    +--+------------------------------+-------+---------+---------+
       Total:                         6204    3174      106    
                                                               
    .\BSP\OLED\
       oled_hardware_i2c.o            1686    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1686    2072      0      
                                                               
    .\System\
       clock.o                        152     0         8      
       interrupt.o                    36      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         188     0         8      
                                                               
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       192     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         202     0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5644    291       4      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2654    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       81        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17586   5902      677    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005bd4 records: 2, size/record: 8, table size: 16
	.data: load addr=00005b90, load size=0000002d bytes, run addr=20200000, run size=00000072 bytes, compression=lzss
	.bss: load addr=00005bcc, load size=00000008 bytes, run addr=20200074, run size=00000034 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005bc0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001931     0000453c     0000453a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                           
-------   ----                           
00001ac3  ADC0_IRQHandler                
00001ac3  ADC1_IRQHandler                
00001ac3  AES_IRQHandler                 
00004576  C$$EXIT                        
00001ac3  CANFD0_IRQHandler              
00001ac3  DAC0_IRQHandler                
00004529  DL_Common_delayCycles          
00003b21  DL_I2C_fillControllerTXFIFO    
00004049  DL_I2C_flushControllerTXFIFO   
000042e9  DL_I2C_setClockConfig          
00001ac3  DMA_IRQHandler                 
00001ac3  Default_Handler                
00001ac3  GROUP0_IRQHandler              
0000444d  GROUP1_IRQHandler              
00004577  HOSTexit                       
00001ac3  HardFault_Handler              
00001ac3  I2C0_IRQHandler                
00001ac3  I2C1_IRQHandler                
000031c9  MPU6050_Init                   
00001ac3  NMI_Handler                    
00001ee5  OLED_Clear                     
00000a91  OLED_Init                      
00002ce5  OLED_ShowChar                  
00003665  OLED_ShowString                
00002165  OLED_WR_Byte                   
00001ac3  PendSV_Handler                 
00001ac3  RTC_IRQHandler                 
00001ac5  Read_Quad                      
0000457b  Reset_Handler                  
00001ac3  SPI0_IRQHandler                
00001ac3  SPI1_IRQHandler                
00001ac3  SVC_Handler                    
00003849  SYSCFG_DL_GPIO_init            
00003ce5  SYSCFG_DL_I2C_MPU6050_init     
00003d39  SYSCFG_DL_I2C_OLED_init        
00003d8d  SYSCFG_DL_SYSCTL_init          
000043ed  SYSCFG_DL_init                 
00003f09  SYSCFG_DL_initPower            
000044b1  SysTick_Handler                
00003f49  SysTick_Init                   
00003755  SystemTime_GetMs               
00001ac3  TIMA0_IRQHandler               
00001ac3  TIMA1_IRQHandler               
00001ac3  TIMG0_IRQHandler               
00001ac3  TIMG12_IRQHandler              
00001ac3  TIMG6_IRQHandler               
00001ac3  TIMG7_IRQHandler               
00001ac3  TIMG8_IRQHandler               
00001ac3  UART0_IRQHandler               
00001ac3  UART1_IRQHandler               
00001ac3  UART2_IRQHandler               
00001ac3  UART3_IRQHandler               
20208000  __STACK_END                    
00000200  __STACK_SIZE                   
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00005bd4  __TI_CINIT_Base                
00005be4  __TI_CINIT_Limit               
00005be4  __TI_CINIT_Warm                
00005bc0  __TI_Handler_Table_Base        
00005bcc  __TI_Handler_Table_Limit       
000040c1  __TI_auto_init_nobinit_nopinit 
000035e9  __TI_decompress_lzss           
0000449d  __TI_decompress_none           
00003bdd  __TI_ltoa                      
ffffffff  __TI_pprof_out_hndl            
000000c1  __TI_printfi                   
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
00000000  __TI_static_base__             
00004435  __TI_zero_init_nomemset        
0000193b  __adddf3                       
00002f7f  __addsf3                       
000059a0  __aeabi_ctype_table_           
000059a0  __aeabi_ctype_table_C          
00003761  __aeabi_d2f                    
00003e35  __aeabi_d2iz                   
0000193b  __aeabi_dadd                   
00003a5d  __aeabi_dcmpeq                 
00003a99  __aeabi_dcmpge                 
00003aad  __aeabi_dcmpgt                 
00003a85  __aeabi_dcmple                 
00003a71  __aeabi_dcmplt                 
00002739  __aeabi_ddiv                   
00002b1d  __aeabi_dmul                   
00001931  __aeabi_dsub                   
20200050  __aeabi_errno                  
00004561  __aeabi_errno_addr             
00003fc9  __aeabi_f2d                    
00004139  __aeabi_f2iz                   
00002f7f  __aeabi_fadd                   
00003565  __aeabi_fdiv                   
0000344d  __aeabi_fmul                   
00002f75  __aeabi_fsub                   
00004241  __aeabi_i2d                    
00004085  __aeabi_i2f                    
00003c8d  __aeabi_idiv                   
000035e7  __aeabi_idiv0                  
00003c8d  __aeabi_idivmod                
000036df  __aeabi_ldiv0                  
00004379  __aeabi_llsl                   
00004311  __aeabi_lmul                   
00004569  __aeabi_memcpy                 
00004569  __aeabi_memcpy4                
00004569  __aeabi_memcpy8                
000044d1  __aeabi_memset                 
000044d1  __aeabi_memset4                
000044d1  __aeabi_memset8                
00004299  __aeabi_ui2f                   
00003f89  __aeabi_uidiv                  
00003f89  __aeabi_uidivmod               
00004461  __aeabi_uldivmod               
00004379  __ashldi3                      
ffffffff  __binit__                      
00003925  __cmpdf2                       
00002739  __divdf3                       
00003565  __divsf3                       
00003925  __eqdf2                        
00003fc9  __extendsfdf2                  
00003e35  __fixdfsi                      
00004139  __fixsfsi                      
00004241  __floatsidf                    
00004085  __floatsisf                    
00004299  __floatunsisf                  
000036e1  __gedf2                        
000036e1  __gtdf2                        
00003925  __ledf2                        
00003925  __ltdf2                        
UNDEFED   __mpu_init                     
00002b1d  __muldf3                       
00004311  __muldi3                       
000040fd  __muldsi3                      
0000344d  __mulsf3                       
00003925  __nedf2                        
20207e00  __stack                        
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
00001931  __subdf3                       
00002f75  __subsf3                       
00003761  __truncdfsf2                   
00003311  __udivmoddi4                   
000042c1  _c_int00_noargs                
UNDEFED   _system_post_cinit             
0000457f  _system_pre_init               
00004571  abort                          
20200084  accel                          
00005776  asc2_0806                      
00005186  asc2_1608                      
00004009  atoi                           
ffffffff  binit                          
00000d55  dmp_enable_feature             
000043b5  dmp_load_motion_driver_firmware
000015e5  dmp_read_fifo                  
000044ed  dmp_register_android_orient_cb 
000044f9  dmp_register_tap_cb            
0000398d  dmp_set_fifo_rate              
00002a31  dmp_set_orientation            
0000229d  dmp_set_tap_thresh             
00003b81  frexp                          
00003b81  frexpl                         
2020008a  gyro                           
00005b4e  hw                             
00000000  interruptVectors               
00002e9d  ldexp                          
00002e9d  ldexpl                         
00004505  led_blink_task                 
00004397  main                           
00004335  memccpy                        
000033b3  memcpy                         
00003abf  memset                         
202000a6  more                           
00004511  mpu6050_clear_data_ready       
0000426d  mpu6050_get_angles             
000034d9  mpu6050_i2c_sda_unlock         
0000451d  mpu6050_is_data_ready          
00003e81  mpu6050_task                   
0000311d  mpu_configure_fifo             
000041a9  mpu_get_accel_fsr              
00004359  mpu_get_gyro_fsr               
000043d1  mpu_get_sample_rate            
0000293d  mpu_init                       
00001d9d  mpu_load_firmware              
00001225  mpu_lp_accel_mode              
0000326d  mpu_read_fifo_stream           
00001c4d  mpu_reset_fifo                 
00002845  mpu_set_bypass                 
0000304d  mpu_set_dmp_state              
000037d5  mpu_set_lpf                    
00002c01  mpu_set_sample_rate            
00002dc1  mpu_set_sensors                
000038b9  mpu_write_mem                  
000041dd  mspm0_delay_ms                 
0000441d  mspm0_get_clock_ms             
0000178d  mspm0_i2c_read                 
000023d1  mspm0_i2c_write                
00002621  oled_task                      
20200090  pitch                          
20200074  quat                           
00005acc  reg                            
20200094  roll                           
00002e9d  scalbn                         
00002e9d  scalbnl                        
00004475  scheduler_init                 
00003ec5  scheduler_run                  
20200098  sensor_timestamp               
202000a4  sensors                        
00004171  sprintf                        
2020009c  start_time                     
20200064  sys_tick                       
202000a7  task_num                       
00005aa4  test                           
000044c1  wcslen                         
202000a0  yaw                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                           
-------   ----                           
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00000000  __TI_static_base__             
00000000  interruptVectors               
000000c1  __TI_printfi                   
00000200  __STACK_SIZE                   
00000a91  OLED_Init                      
00000d55  dmp_enable_feature             
00001225  mpu_lp_accel_mode              
000015e5  dmp_read_fifo                  
0000178d  mspm0_i2c_read                 
00001931  __aeabi_dsub                   
00001931  __subdf3                       
0000193b  __adddf3                       
0000193b  __aeabi_dadd                   
00001ac3  ADC0_IRQHandler                
00001ac3  ADC1_IRQHandler                
00001ac3  AES_IRQHandler                 
00001ac3  CANFD0_IRQHandler              
00001ac3  DAC0_IRQHandler                
00001ac3  DMA_IRQHandler                 
00001ac3  Default_Handler                
00001ac3  GROUP0_IRQHandler              
00001ac3  HardFault_Handler              
00001ac3  I2C0_IRQHandler                
00001ac3  I2C1_IRQHandler                
00001ac3  NMI_Handler                    
00001ac3  PendSV_Handler                 
00001ac3  RTC_IRQHandler                 
00001ac3  SPI0_IRQHandler                
00001ac3  SPI1_IRQHandler                
00001ac3  SVC_Handler                    
00001ac3  TIMA0_IRQHandler               
00001ac3  TIMA1_IRQHandler               
00001ac3  TIMG0_IRQHandler               
00001ac3  TIMG12_IRQHandler              
00001ac3  TIMG6_IRQHandler               
00001ac3  TIMG7_IRQHandler               
00001ac3  TIMG8_IRQHandler               
00001ac3  UART0_IRQHandler               
00001ac3  UART1_IRQHandler               
00001ac3  UART2_IRQHandler               
00001ac3  UART3_IRQHandler               
00001ac5  Read_Quad                      
00001c4d  mpu_reset_fifo                 
00001d9d  mpu_load_firmware              
00001ee5  OLED_Clear                     
00002165  OLED_WR_Byte                   
0000229d  dmp_set_tap_thresh             
000023d1  mspm0_i2c_write                
00002621  oled_task                      
00002739  __aeabi_ddiv                   
00002739  __divdf3                       
00002845  mpu_set_bypass                 
0000293d  mpu_init                       
00002a31  dmp_set_orientation            
00002b1d  __aeabi_dmul                   
00002b1d  __muldf3                       
00002c01  mpu_set_sample_rate            
00002ce5  OLED_ShowChar                  
00002dc1  mpu_set_sensors                
00002e9d  ldexp                          
00002e9d  ldexpl                         
00002e9d  scalbn                         
00002e9d  scalbnl                        
00002f75  __aeabi_fsub                   
00002f75  __subsf3                       
00002f7f  __addsf3                       
00002f7f  __aeabi_fadd                   
0000304d  mpu_set_dmp_state              
0000311d  mpu_configure_fifo             
000031c9  MPU6050_Init                   
0000326d  mpu_read_fifo_stream           
00003311  __udivmoddi4                   
000033b3  memcpy                         
0000344d  __aeabi_fmul                   
0000344d  __mulsf3                       
000034d9  mpu6050_i2c_sda_unlock         
00003565  __aeabi_fdiv                   
00003565  __divsf3                       
000035e7  __aeabi_idiv0                  
000035e9  __TI_decompress_lzss           
00003665  OLED_ShowString                
000036df  __aeabi_ldiv0                  
000036e1  __gedf2                        
000036e1  __gtdf2                        
00003755  SystemTime_GetMs               
00003761  __aeabi_d2f                    
00003761  __truncdfsf2                   
000037d5  mpu_set_lpf                    
00003849  SYSCFG_DL_GPIO_init            
000038b9  mpu_write_mem                  
00003925  __cmpdf2                       
00003925  __eqdf2                        
00003925  __ledf2                        
00003925  __ltdf2                        
00003925  __nedf2                        
0000398d  dmp_set_fifo_rate              
00003a5d  __aeabi_dcmpeq                 
00003a71  __aeabi_dcmplt                 
00003a85  __aeabi_dcmple                 
00003a99  __aeabi_dcmpge                 
00003aad  __aeabi_dcmpgt                 
00003abf  memset                         
00003b21  DL_I2C_fillControllerTXFIFO    
00003b81  frexp                          
00003b81  frexpl                         
00003bdd  __TI_ltoa                      
00003c8d  __aeabi_idiv                   
00003c8d  __aeabi_idivmod                
00003ce5  SYSCFG_DL_I2C_MPU6050_init     
00003d39  SYSCFG_DL_I2C_OLED_init        
00003d8d  SYSCFG_DL_SYSCTL_init          
00003e35  __aeabi_d2iz                   
00003e35  __fixdfsi                      
00003e81  mpu6050_task                   
00003ec5  scheduler_run                  
00003f09  SYSCFG_DL_initPower            
00003f49  SysTick_Init                   
00003f89  __aeabi_uidiv                  
00003f89  __aeabi_uidivmod               
00003fc9  __aeabi_f2d                    
00003fc9  __extendsfdf2                  
00004009  atoi                           
00004049  DL_I2C_flushControllerTXFIFO   
00004085  __aeabi_i2f                    
00004085  __floatsisf                    
000040c1  __TI_auto_init_nobinit_nopinit 
000040fd  __muldsi3                      
00004139  __aeabi_f2iz                   
00004139  __fixsfsi                      
00004171  sprintf                        
000041a9  mpu_get_accel_fsr              
000041dd  mspm0_delay_ms                 
00004241  __aeabi_i2d                    
00004241  __floatsidf                    
0000426d  mpu6050_get_angles             
00004299  __aeabi_ui2f                   
00004299  __floatunsisf                  
000042c1  _c_int00_noargs                
000042e9  DL_I2C_setClockConfig          
00004311  __aeabi_lmul                   
00004311  __muldi3                       
00004335  memccpy                        
00004359  mpu_get_gyro_fsr               
00004379  __aeabi_llsl                   
00004379  __ashldi3                      
00004397  main                           
000043b5  dmp_load_motion_driver_firmware
000043d1  mpu_get_sample_rate            
000043ed  SYSCFG_DL_init                 
0000441d  mspm0_get_clock_ms             
00004435  __TI_zero_init_nomemset        
0000444d  GROUP1_IRQHandler              
00004461  __aeabi_uldivmod               
00004475  scheduler_init                 
0000449d  __TI_decompress_none           
000044b1  SysTick_Handler                
000044c1  wcslen                         
000044d1  __aeabi_memset                 
000044d1  __aeabi_memset4                
000044d1  __aeabi_memset8                
000044ed  dmp_register_android_orient_cb 
000044f9  dmp_register_tap_cb            
00004505  led_blink_task                 
00004511  mpu6050_clear_data_ready       
0000451d  mpu6050_is_data_ready          
00004529  DL_Common_delayCycles          
00004561  __aeabi_errno_addr             
00004569  __aeabi_memcpy                 
00004569  __aeabi_memcpy4                
00004569  __aeabi_memcpy8                
00004571  abort                          
00004576  C$$EXIT                        
00004577  HOSTexit                       
0000457b  Reset_Handler                  
0000457f  _system_pre_init               
00005186  asc2_1608                      
00005776  asc2_0806                      
000059a0  __aeabi_ctype_table_           
000059a0  __aeabi_ctype_table_C          
00005aa4  test                           
00005acc  reg                            
00005b4e  hw                             
00005bc0  __TI_Handler_Table_Base        
00005bcc  __TI_Handler_Table_Limit       
00005bd4  __TI_CINIT_Base                
00005be4  __TI_CINIT_Limit               
00005be4  __TI_CINIT_Warm                
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
20200050  __aeabi_errno                  
20200064  sys_tick                       
20200074  quat                           
20200084  accel                          
2020008a  gyro                           
20200090  pitch                          
20200094  roll                           
20200098  sensor_timestamp               
2020009c  start_time                     
202000a0  yaw                            
202000a4  sensors                        
202000a6  more                           
202000a7  task_num                       
20207e00  __stack                        
20208000  __STACK_END                    
ffffffff  __TI_pprof_out_hndl            
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
ffffffff  __binit__                      
ffffffff  binit                          
UNDEFED   __mpu_init                     
UNDEFED   _system_post_cinit             

[222 symbols]

******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jun 26 21:40:09 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004c71


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006628  000199d8  R  X
  SRAM                  20200000   00008000  000002a6  00007d5a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006628   00006628    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004ee0   00004ee0    r-x .text
  00004fa0    00004fa0    00001630   00001630    r-- .rodata
  000065d0    000065d0    00000058   00000058    r-- .cinit
20200000    20200000    000000a8   00000000    rw-
  20200000    20200000    00000072   00000000    rw- .data
  20200074    20200074    00000034   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004ee0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002c4     oled_hardware_i2c.o (.text.OLED_Init)
                  000013b0    000002b0     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001660    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001880    00000210     mpu6050.o (.text.Read_Quad)
                  00001a90    000001e4     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00001c74    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001e50    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001ff8    000001a4     mspm0_i2c.o (.text.mspm0_i2c_read)
                  0000219c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000232e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002330    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000024b8    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00002628    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  00002778    00000148     inv_mpu.o (.text.mpu_load_firmware)
                  000028c0    00000144     oled_hardware_i2c.o (.text.OLED_Clear)
                  00002a04    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002b40    00000138     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00002c78    00000134     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00002dac    00000130     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00002edc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002ffc    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003108    000000f8     inv_mpu.o (.text.mpu_set_bypass)
                  00003200    000000f4     inv_mpu.o (.text.mpu_init)
                  000032f4    000000ec     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  000033e0    000000ec     oled_app.o (.text.oled_task)
                  000034cc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000035b0    000000e4     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003694    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00003770    000000dc     inv_mpu.o (.text.mpu_set_sensors)
                  0000384c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003924    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000039fc    000000d0     inv_mpu.o (.text.mpu_set_dmp_state)
                  00003acc    000000b8     mpu6050.o (.text.MPU6050_Init)
                  00003b84    000000ac     inv_mpu.o (.text.mpu_configure_fifo)
                  00003c30    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003cd4    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003d76    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00003e10    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003e9c    0000008c     mspm0_i2c.o (.text.mpu6050_i2c_sda_unlock)
                  00003f28    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003faa    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00003fac    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00004028    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  000040a2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000040a4    00000074                            : comparedf2.c.obj (.text.__gedf2)
                  00004118    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004120    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004194    00000074     inv_mpu.o (.text.mpu_set_lpf)
                  00004208    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00004278    0000006c     inv_mpu.o (.text.mpu_write_mem)
                  000042e4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000434c    00000068     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000043b4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000441a    00000002     mpu6050.o (.text.android_orient_cb)
                  0000441c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000447e    00000062     libc.a : memset16.S.obj (.text:memset)
                  000044e0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000453e    00000002     mpu6050.o (.text.tap_cb)
                  00004540    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000459c    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  000045f4    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000464c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000046a2    00000002     --HOLE-- [fill = 0]
                  000046a4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  000046f8    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000474c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000047a0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000047f2    00000002     --HOLE-- [fill = 0]
                  000047f4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000483e    00000002     --HOLE-- [fill = 0]
                  00004840    00000044     scheduler.o (.text.scheduler_run)
                  00004884    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000048c4    00000040     clock.o (.text.SysTick_Init)
                  00004904    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004944    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004984    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000049c4    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004a00    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004a3c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004a78    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00004ab2    00000002     --HOLE-- [fill = 0]
                  00004ab4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00004aec    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004b24    00000034     mpu6050_app.o (.text.mpu6050_task)
                  00004b58    00000034     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00004b8c    00000034     clock.o (.text.mspm0_delay_ms)
                  00004bc0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004bf0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004c1c    0000002c     mpu6050_app.o (.text.mpu6050_get_angles)
                  00004c48    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00004c70    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004c98    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004cbe    00000002     --HOLE-- [fill = 0]
                  00004cc0    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004ce4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004d06    00000002     --HOLE-- [fill = 0]
                  00004d08    00000020     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00004d28    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004d46    0000001e     empty.o (.text.main)
                  00004d64    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00004d80    0000001c     inv_mpu.o (.text.mpu_get_sample_rate)
                  00004d9c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004db4    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00004dcc    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00004de4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004dfa    00000002     --HOLE-- [fill = 0]
                  00004dfc    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  00004e10    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004e24    00000014     scheduler.o (.text.scheduler_init)
                  00004e38    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004e4c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004e5e    00000002     --HOLE-- [fill = 0]
                  00004e60    00000010     interrupt.o (.text.SysTick_Handler)
                  00004e70    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004e80    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004e8e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004e9c    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00004ea8    0000000c     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00004eb4    0000000c     empty.o (.text.led_blink_task)
                  00004ec0    0000000c     mpu6050_app.o (.text.mpu6050_clear_data_ready)
                  00004ecc    0000000c     mpu6050_app.o (.text.mpu6050_is_data_ready)
                  00004ed8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004ee2    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004eec    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004efc    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004f06    00000002     --HOLE-- [fill = 0]
                  00004f08    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00004f18    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004f22    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004f2c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004f36    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00004f40    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00004f50    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00004f5a    00000008            : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004f62    00000002     --HOLE-- [fill = 0]
                  00004f64    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004f6c    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004f72    00000002     --HOLE-- [fill = 0]
                  00004f74    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00004f84    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004f8a    00000006            : exit.c.obj (.text:abort)
                  00004f90    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004f94    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00004f98    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004f9c    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000065d0    00000058     
                  000065d0    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  000065ff    00000001     --HOLE-- [fill = 0]
                  00006600    0000000c     (__TI_handler_table)
                  0000660c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006614    00000010     (__TI_cinit_table)
                  00006624    00000004     --HOLE-- [fill = 0]

.rodata    0    00004fa0    00001630     
                  00004fa0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00005b96    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00006186    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  000063ae    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000063b0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000064b1    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.accel_axes)
                  000064b4    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.gyro_axes)
                  000064b7    00000001     --HOLE-- [fill = 0]
                  000064b8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000064f8    00000028     inv_mpu.o (.rodata.test)
                  00006520    0000001e     inv_mpu.o (.rodata.reg)
                  0000653e    00000018     inv_mpu.o (.rodata.cst8)
                  00006556    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00006567    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00006578    00000010     oled_app.o (.rodata.str1.4119433759365661616.1)
                  00006588    0000000c     inv_mpu.o (.rodata.hw)
                  00006594    0000000c     oled_app.o (.rodata.str1.10123847253219111340.1)
                  000065a0    0000000c     oled_app.o (.rodata.str1.17288060079586621852.1)
                  000065ac    0000000c     oled_app.o (.rodata.str1.865768355259620247.1)
                  000065b8    0000000b     oled_app.o (.rodata.str1.3320576987535119861.1)
                  000065c3    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000065c5    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000072     UNINITIALIZED
                  20200000    0000002c     inv_mpu.o (.data.st)
                  2020002c    00000024     scheduler.o (.data.scheduler_task)
                  20200050    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200054    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200058    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  2020005c    00000004     mpu6050_app.o (.data.mpu6050_task.last_update_time)
                  20200060    00000004     oled_app.o (.data.oled_task.counter)
                  20200064    00000004     clock.o (.data.sys_tick)
                  20200068    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.2)
                  2020006a    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)
                  2020006b    00000001     mpu6050_app.o (.data.mpu6050_data_ready)
                  2020006c    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  2020006e    00000002     --HOLE--
                  20200070    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.4)

.bss       0    20200074    00000034     UNINITIALIZED
                  20200074    00000010     (.common:quat)
                  20200084    00000006     (.common:accel)
                  2020008a    00000006     (.common:gyro)
                  20200090    00000004     (.common:pitch)
                  20200094    00000004     (.common:roll)
                  20200098    00000004     (.common:sensor_timestamp)
                  2020009c    00000004     (.common:start_time)
                  202000a0    00000004     (.common:yaw)
                  202000a4    00000002     (.common:sensors)
                  202000a6    00000001     (.common:more)
                  202000a7    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             452     4         0      
       startup_mspm0g350x_ticlang.o   6       192       0      
       scheduler.o                    88      0         37     
       empty.o                        42      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         588     196       37     
                                                               
    .\APP\
       oled_app.o                     236     63        4      
       mpu6050_app.o                  120     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         356     63        9      
                                                               
    .\BSP\MPU6050\
       inv_mpu_dmp_motion_driver.o    1812    3068      15     
       inv_mpu.o                      2968    106       44     
       mspm0_i2c.o                    864     0         0      
       mpu6050.o                      716     0         47     
    +--+------------------------------+-------+---------+---------+
       Total:                         6360    3174      106    
                                                               
    .\BSP\OLED\
       oled_hardware_i2c.o            1686    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1686    2072      0      
                                                               
    .\System\
       clock.o                        140     0         8      
       interrupt.o                    36      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         176     0         8      
                                                               
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       192     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         202     0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8096    355       4      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2702    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       83        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   20170   5943      676    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006614 records: 2, size/record: 8, table size: 16
	.data: load addr=000065d0, load size=0000002f bytes, run addr=20200000, run size=00000072 bytes, compression=lzss
	.bss: load addr=0000660c, load size=00000008 bytes, run addr=20200074, run size=00000034 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006600 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000219d     00004eec     00004eea   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000034cd     00004f08     00004f04   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004f20          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004f34          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004f60          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00004f88          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002ffd     00004f40     00004f3e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000021a7     00004f74     00004f70   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004f96          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)

[4 trampolines]
[9 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                           
-------   ----                           
0000232f  ADC0_IRQHandler                
0000232f  ADC1_IRQHandler                
0000232f  AES_IRQHandler                 
00004f90  C$$EXIT                        
0000232f  CANFD0_IRQHandler              
0000232f  DAC0_IRQHandler                
00004ed9  DL_Common_delayCycles          
000044e1  DL_I2C_fillControllerTXFIFO    
000049c5  DL_I2C_flushControllerTXFIFO   
00004c99  DL_I2C_setClockConfig          
0000232f  DMA_IRQHandler                 
0000232f  Default_Handler                
0000232f  GROUP0_IRQHandler              
00004dfd  GROUP1_IRQHandler              
00004f91  HOSTexit                       
0000232f  HardFault_Handler              
0000232f  I2C0_IRQHandler                
0000232f  I2C1_IRQHandler                
00003acd  MPU6050_Init                   
0000232f  NMI_Handler                    
000028c1  OLED_Clear                     
000010ed  OLED_Init                      
UNDEFED   OLED_Refresh                   
00003695  OLED_ShowChar                  
00004029  OLED_ShowString                
00002b41  OLED_WR_Byte                   
0000232f  PendSV_Handler                 
0000232f  RTC_IRQHandler                 
00001881  Read_Quad                      
00004f99  Reset_Handler                  
0000232f  SPI0_IRQHandler                
0000232f  SPI1_IRQHandler                
0000232f  SVC_Handler                    
00004209  SYSCFG_DL_GPIO_init            
000046a5  SYSCFG_DL_I2C_MPU6050_init     
000046f9  SYSCFG_DL_I2C_OLED_init        
0000474d  SYSCFG_DL_SYSCTL_init          
00004d9d  SYSCFG_DL_init                 
00004885  SYSCFG_DL_initPower            
00004e61  SysTick_Handler                
000048c5  SysTick_Init                   
0000232f  TIMA0_IRQHandler               
0000232f  TIMA1_IRQHandler               
0000232f  TIMG0_IRQHandler               
0000232f  TIMG12_IRQHandler              
0000232f  TIMG6_IRQHandler               
0000232f  TIMG7_IRQHandler               
0000232f  TIMG8_IRQHandler               
0000232f  UART0_IRQHandler               
0000232f  UART1_IRQHandler               
0000232f  UART2_IRQHandler               
0000232f  UART3_IRQHandler               
20208000  __STACK_END                    
00000200  __STACK_SIZE                   
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00006614  __TI_CINIT_Base                
00006624  __TI_CINIT_Limit               
00006624  __TI_CINIT_Warm                
00006600  __TI_Handler_Table_Base        
0000660c  __TI_Handler_Table_Limit       
00004a3d  __TI_auto_init_nobinit_nopinit 
00003fad  __TI_decompress_lzss           
00004e4d  __TI_decompress_none           
0000459d  __TI_ltoa                      
ffffffff  __TI_pprof_out_hndl            
000000c1  __TI_printfi                   
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
00000000  __TI_static_base__             
00004de5  __TI_zero_init_nomemset        
000021a7  __adddf3                       
0000392f  __addsf3                       
000063b0  __aeabi_ctype_table_           
000063b0  __aeabi_ctype_table_C          
00004121  __aeabi_d2f                    
000047f5  __aeabi_d2iz                   
000021a7  __aeabi_dadd                   
0000441d  __aeabi_dcmpeq                 
00004459  __aeabi_dcmpge                 
0000446d  __aeabi_dcmpgt                 
00004445  __aeabi_dcmple                 
00004431  __aeabi_dcmplt                 
00002ffd  __aeabi_ddiv                   
000034cd  __aeabi_dmul                   
0000219d  __aeabi_dsub                   
20200050  __aeabi_errno                  
00004119  __aeabi_errno_addr             
00004945  __aeabi_f2d                    
00004ab5  __aeabi_f2iz                   
0000392f  __aeabi_fadd                   
00003f29  __aeabi_fdiv                   
00003e11  __aeabi_fmul                   
00003925  __aeabi_fsub                   
00004bf1  __aeabi_i2d                    
00004a01  __aeabi_i2f                    
0000464d  __aeabi_idiv                   
00003fab  __aeabi_idiv0                  
0000464d  __aeabi_idivmod                
000040a3  __aeabi_ldiv0                  
00004d29  __aeabi_llsl                   
00004cc1  __aeabi_lmul                   
00004f65  __aeabi_memcpy                 
00004f65  __aeabi_memcpy4                
00004f65  __aeabi_memcpy8                
00004e81  __aeabi_memset                 
00004e81  __aeabi_memset4                
00004e81  __aeabi_memset8                
00004c49  __aeabi_ui2f                   
00004905  __aeabi_uidiv                  
00004905  __aeabi_uidivmod               
00004e11  __aeabi_uldivmod               
00004d29  __ashldi3                      
ffffffff  __binit__                      
000042e5  __cmpdf2                       
00002ffd  __divdf3                       
00003f29  __divsf3                       
000042e5  __eqdf2                        
00004945  __extendsfdf2                  
000047f5  __fixdfsi                      
00004ab5  __fixsfsi                      
00004bf1  __floatsidf                    
00004a01  __floatsisf                    
00004c49  __floatunsisf                  
000040a5  __gedf2                        
000040a5  __gtdf2                        
000042e5  __ledf2                        
000042e5  __ltdf2                        
UNDEFED   __mpu_init                     
000034cd  __muldf3                       
00004cc1  __muldi3                       
00004a79  __muldsi3                      
00003e11  __mulsf3                       
000042e5  __nedf2                        
20207e00  __stack                        
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
0000219d  __subdf3                       
00003925  __subsf3                       
00004121  __truncdfsf2                   
00003cd5  __udivmoddi4                   
00004c71  _c_int00_noargs                
UNDEFED   _system_post_cinit             
00004f9d  _system_pre_init               
00004f8b  abort                          
20200084  accel                          
00006186  asc2_0806                      
00005b96  asc2_1608                      
00000a91  asin                           
00000a91  asinl                          
00000df5  atan                           
00002331  atan2                          
00002331  atan2l                         
00000df5  atanl                          
00004985  atoi                           
ffffffff  binit                          
000013b1  dmp_enable_feature             
00004d65  dmp_load_motion_driver_firmware
00001e51  dmp_read_fifo                  
00004e9d  dmp_register_android_orient_cb 
00004ea9  dmp_register_tap_cb            
0000434d  dmp_set_fifo_rate              
000032f5  dmp_set_orientation            
00002c79  dmp_set_tap_thresh             
00004541  frexp                          
00004541  frexpl                         
2020008a  gyro                           
00006588  hw                             
00000000  interruptVectors               
0000384d  ldexp                          
0000384d  ldexpl                         
00004eb5  led_blink_task                 
00004d47  main                           
00004ce5  memccpy                        
00003d77  memcpy                         
0000447f  memset                         
202000a6  more                           
00004ec1  mpu6050_clear_data_ready       
00004c1d  mpu6050_get_angles             
00003e9d  mpu6050_i2c_sda_unlock         
00004ecd  mpu6050_is_data_ready          
00004b25  mpu6050_task                   
00003b85  mpu_configure_fifo             
00004b59  mpu_get_accel_fsr              
00004d09  mpu_get_gyro_fsr               
00004d81  mpu_get_sample_rate            
00003201  mpu_init                       
00002779  mpu_load_firmware              
00001a91  mpu_lp_accel_mode              
00003c31  mpu_read_fifo_stream           
00002629  mpu_reset_fifo                 
00003109  mpu_set_bypass                 
000039fd  mpu_set_dmp_state              
00004195  mpu_set_lpf                    
000035b1  mpu_set_sample_rate            
00003771  mpu_set_sensors                
00004279  mpu_write_mem                  
00004b8d  mspm0_delay_ms                 
00004dcd  mspm0_get_clock_ms             
00001ff9  mspm0_i2c_read                 
00002dad  mspm0_i2c_write                
000033e1  oled_task                      
20200090  pitch                          
20200074  quat                           
00006520  reg                            
20200094  roll                           
0000384d  scalbn                         
0000384d  scalbnl                        
00004e25  scheduler_init                 
00004841  scheduler_run                  
20200098  sensor_timestamp               
202000a4  sensors                        
00004aed  sprintf                        
000024b9  sqrt                           
000024b9  sqrtl                          
2020009c  start_time                     
20200064  sys_tick                       
202000a7  task_num                       
000064f8  test                           
00004e71  wcslen                         
202000a0  yaw                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                           
-------   ----                           
00000000  __TI_ATRegion0_region_sz       
00000000  __TI_ATRegion0_src_addr        
00000000  __TI_ATRegion0_trg_addr        
00000000  __TI_ATRegion1_region_sz       
00000000  __TI_ATRegion1_src_addr        
00000000  __TI_ATRegion1_trg_addr        
00000000  __TI_ATRegion2_region_sz       
00000000  __TI_ATRegion2_src_addr        
00000000  __TI_ATRegion2_trg_addr        
00000000  __TI_static_base__             
00000000  interruptVectors               
000000c1  __TI_printfi                   
00000200  __STACK_SIZE                   
00000a91  asin                           
00000a91  asinl                          
00000df5  atan                           
00000df5  atanl                          
000010ed  OLED_Init                      
000013b1  dmp_enable_feature             
00001881  Read_Quad                      
00001a91  mpu_lp_accel_mode              
00001e51  dmp_read_fifo                  
00001ff9  mspm0_i2c_read                 
0000219d  __aeabi_dsub                   
0000219d  __subdf3                       
000021a7  __adddf3                       
000021a7  __aeabi_dadd                   
0000232f  ADC0_IRQHandler                
0000232f  ADC1_IRQHandler                
0000232f  AES_IRQHandler                 
0000232f  CANFD0_IRQHandler              
0000232f  DAC0_IRQHandler                
0000232f  DMA_IRQHandler                 
0000232f  Default_Handler                
0000232f  GROUP0_IRQHandler              
0000232f  HardFault_Handler              
0000232f  I2C0_IRQHandler                
0000232f  I2C1_IRQHandler                
0000232f  NMI_Handler                    
0000232f  PendSV_Handler                 
0000232f  RTC_IRQHandler                 
0000232f  SPI0_IRQHandler                
0000232f  SPI1_IRQHandler                
0000232f  SVC_Handler                    
0000232f  TIMA0_IRQHandler               
0000232f  TIMA1_IRQHandler               
0000232f  TIMG0_IRQHandler               
0000232f  TIMG12_IRQHandler              
0000232f  TIMG6_IRQHandler               
0000232f  TIMG7_IRQHandler               
0000232f  TIMG8_IRQHandler               
0000232f  UART0_IRQHandler               
0000232f  UART1_IRQHandler               
0000232f  UART2_IRQHandler               
0000232f  UART3_IRQHandler               
00002331  atan2                          
00002331  atan2l                         
000024b9  sqrt                           
000024b9  sqrtl                          
00002629  mpu_reset_fifo                 
00002779  mpu_load_firmware              
000028c1  OLED_Clear                     
00002b41  OLED_WR_Byte                   
00002c79  dmp_set_tap_thresh             
00002dad  mspm0_i2c_write                
00002ffd  __aeabi_ddiv                   
00002ffd  __divdf3                       
00003109  mpu_set_bypass                 
00003201  mpu_init                       
000032f5  dmp_set_orientation            
000033e1  oled_task                      
000034cd  __aeabi_dmul                   
000034cd  __muldf3                       
000035b1  mpu_set_sample_rate            
00003695  OLED_ShowChar                  
00003771  mpu_set_sensors                
0000384d  ldexp                          
0000384d  ldexpl                         
0000384d  scalbn                         
0000384d  scalbnl                        
00003925  __aeabi_fsub                   
00003925  __subsf3                       
0000392f  __addsf3                       
0000392f  __aeabi_fadd                   
000039fd  mpu_set_dmp_state              
00003acd  MPU6050_Init                   
00003b85  mpu_configure_fifo             
00003c31  mpu_read_fifo_stream           
00003cd5  __udivmoddi4                   
00003d77  memcpy                         
00003e11  __aeabi_fmul                   
00003e11  __mulsf3                       
00003e9d  mpu6050_i2c_sda_unlock         
00003f29  __aeabi_fdiv                   
00003f29  __divsf3                       
00003fab  __aeabi_idiv0                  
00003fad  __TI_decompress_lzss           
00004029  OLED_ShowString                
000040a3  __aeabi_ldiv0                  
000040a5  __gedf2                        
000040a5  __gtdf2                        
00004119  __aeabi_errno_addr             
00004121  __aeabi_d2f                    
00004121  __truncdfsf2                   
00004195  mpu_set_lpf                    
00004209  SYSCFG_DL_GPIO_init            
00004279  mpu_write_mem                  
000042e5  __cmpdf2                       
000042e5  __eqdf2                        
000042e5  __ledf2                        
000042e5  __ltdf2                        
000042e5  __nedf2                        
0000434d  dmp_set_fifo_rate              
0000441d  __aeabi_dcmpeq                 
00004431  __aeabi_dcmplt                 
00004445  __aeabi_dcmple                 
00004459  __aeabi_dcmpge                 
0000446d  __aeabi_dcmpgt                 
0000447f  memset                         
000044e1  DL_I2C_fillControllerTXFIFO    
00004541  frexp                          
00004541  frexpl                         
0000459d  __TI_ltoa                      
0000464d  __aeabi_idiv                   
0000464d  __aeabi_idivmod                
000046a5  SYSCFG_DL_I2C_MPU6050_init     
000046f9  SYSCFG_DL_I2C_OLED_init        
0000474d  SYSCFG_DL_SYSCTL_init          
000047f5  __aeabi_d2iz                   
000047f5  __fixdfsi                      
00004841  scheduler_run                  
00004885  SYSCFG_DL_initPower            
000048c5  SysTick_Init                   
00004905  __aeabi_uidiv                  
00004905  __aeabi_uidivmod               
00004945  __aeabi_f2d                    
00004945  __extendsfdf2                  
00004985  atoi                           
000049c5  DL_I2C_flushControllerTXFIFO   
00004a01  __aeabi_i2f                    
00004a01  __floatsisf                    
00004a3d  __TI_auto_init_nobinit_nopinit 
00004a79  __muldsi3                      
00004ab5  __aeabi_f2iz                   
00004ab5  __fixsfsi                      
00004aed  sprintf                        
00004b25  mpu6050_task                   
00004b59  mpu_get_accel_fsr              
00004b8d  mspm0_delay_ms                 
00004bf1  __aeabi_i2d                    
00004bf1  __floatsidf                    
00004c1d  mpu6050_get_angles             
00004c49  __aeabi_ui2f                   
00004c49  __floatunsisf                  
00004c71  _c_int00_noargs                
00004c99  DL_I2C_setClockConfig          
00004cc1  __aeabi_lmul                   
00004cc1  __muldi3                       
00004ce5  memccpy                        
00004d09  mpu_get_gyro_fsr               
00004d29  __aeabi_llsl                   
00004d29  __ashldi3                      
00004d47  main                           
00004d65  dmp_load_motion_driver_firmware
00004d81  mpu_get_sample_rate            
00004d9d  SYSCFG_DL_init                 
00004dcd  mspm0_get_clock_ms             
00004de5  __TI_zero_init_nomemset        
00004dfd  GROUP1_IRQHandler              
00004e11  __aeabi_uldivmod               
00004e25  scheduler_init                 
00004e4d  __TI_decompress_none           
00004e61  SysTick_Handler                
00004e71  wcslen                         
00004e81  __aeabi_memset                 
00004e81  __aeabi_memset4                
00004e81  __aeabi_memset8                
00004e9d  dmp_register_android_orient_cb 
00004ea9  dmp_register_tap_cb            
00004eb5  led_blink_task                 
00004ec1  mpu6050_clear_data_ready       
00004ecd  mpu6050_is_data_ready          
00004ed9  DL_Common_delayCycles          
00004f65  __aeabi_memcpy                 
00004f65  __aeabi_memcpy4                
00004f65  __aeabi_memcpy8                
00004f8b  abort                          
00004f90  C$$EXIT                        
00004f91  HOSTexit                       
00004f99  Reset_Handler                  
00004f9d  _system_pre_init               
00005b96  asc2_1608                      
00006186  asc2_0806                      
000063b0  __aeabi_ctype_table_           
000063b0  __aeabi_ctype_table_C          
000064f8  test                           
00006520  reg                            
00006588  hw                             
00006600  __TI_Handler_Table_Base        
0000660c  __TI_Handler_Table_Limit       
00006614  __TI_CINIT_Base                
00006624  __TI_CINIT_Limit               
00006624  __TI_CINIT_Warm                
20200000  __start___llvm_prf_bits        
20200000  __start___llvm_prf_cnts        
20200000  __stop___llvm_prf_bits         
20200000  __stop___llvm_prf_cnts         
20200050  __aeabi_errno                  
20200064  sys_tick                       
20200074  quat                           
20200084  accel                          
2020008a  gyro                           
20200090  pitch                          
20200094  roll                           
20200098  sensor_timestamp               
2020009c  start_time                     
202000a0  yaw                            
202000a4  sensors                        
202000a6  more                           
202000a7  task_num                       
20207e00  __stack                        
20208000  __STACK_END                    
ffffffff  __TI_pprof_out_hndl            
ffffffff  __TI_prof_data_size            
ffffffff  __TI_prof_data_start           
ffffffff  __binit__                      
ffffffff  binit                          
UNDEFED   OLED_Refresh                   
UNDEFED   __mpu_init                     
UNDEFED   _system_post_cinit             

[230 symbols]

******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jun 26 23:48:35 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000010dd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001270  0001ed90  R  X
  SRAM                  20200000   00008000  00000276  00007d8a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001270   00001270    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001120   00001120    r-x .text
  000011e0    000011e0    00000058   00000058    r-- .rodata
  00001238    00001238    00000038   00000038    r-- .cinit
20200000    20200000    00000077   00000000    rw-
  20200000    20200000    00000043   00000000    rw- .data
  20200044    20200044    00000033   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001120     
                  000000c0    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00000268    000001a4     mspm0_i2c.o (.text.mspm0_i2c_read)
                  0000040c    00000188     mpu6050.o (.text.Read_Quad)
                  00000594    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  000006e4    00000130     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00000814    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000008f8    000000d8                            : addsf3.S.obj (.text)
                  000009d0    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00000a74    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000b0e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000b10    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00000b9c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000c18    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000c20    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00000c94    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000d04    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00000d62    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000d64    00000058     empty.o (.text.main)
                  00000dbc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00000e10    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000e64    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000eb8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000ef8    00000040     clock.o (.text.SysTick_Init)
                  00000f38    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000f78    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00000fb8    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00000ff4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001030    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000106c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000010a6    00000002     --HOLE-- [fill = 0]
                  000010a8    00000034     clock.o (.text.mspm0_delay_ms)
                  000010dc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001104    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000112a    00000002     --HOLE-- [fill = 0]
                  0000112c    0000001c     interrupt.o (.text.SysTick_Handler)
                  00001148    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001160    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00001178    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000118e    00000002     --HOLE-- [fill = 0]
                  00001190    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  000011a4    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000011b6    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000011c0    00000006     libc.a : exit.c.obj (.text:abort)
                  000011c6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000011ca    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000011ce    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000011d2    0000000e     --HOLE-- [fill = 0]

.cinit     0    00001238    00000038     
                  00001238    00000014     (.cinit..data.load) [load image, compression = lzss]
                  0000124c    0000000c     (__TI_handler_table)
                  00001258    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001260    00000010     (__TI_cinit_table)

.rodata    0    000011e0    00000058     
                  000011e0    00000028     inv_mpu.o (.rodata.test)
                  00001208    0000001e     inv_mpu.o (.rodata.reg)
                  00001226    0000000c     inv_mpu.o (.rodata.hw)
                  00001232    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00001234    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00001236    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000043     UNINITIALIZED
                  20200000    0000002c     inv_mpu.o (.data.st)
                  2020002c    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200030    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  20200034    00000004     empty.o (.data.led_toggle_time)
                  20200038    00000004     clock.o (.data.sys_tick)
                  2020003c    00000004     empty.o (.data.systick_counter)
                  20200040    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  20200042    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)

.bss       0    20200044    00000033     UNINITIALIZED
                  20200044    00000010     (.common:quat)
                  20200054    00000006     (.common:accel)
                  2020005a    00000006     (.common:gyro)
                  20200060    00000004     (.common:pitch)
                  20200064    00000004     (.common:roll)
                  20200068    00000004     (.common:sensor_timestamp)
                  2020006c    00000004     (.common:start_time)
                  20200070    00000004     (.common:yaw)
                  20200074    00000002     (.common:sensors)
                  20200076    00000001     (.common:more)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             452    4         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        88     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         546    196       8      
                                                              
    .\BSP\MPU6050\
       mspm0_i2c.o                    724    0         0      
       inv_mpu.o                      500    82        44     
       mpu6050.o                      392    0         47     
       inv_mpu_dmp_motion_driver.o    424    0         11     
    +--+------------------------------+------+---------+---------+
       Total:                         2040   82        102    
                                                              
    .\System\
       clock.o                        140    0         8      
       interrupt.o                    48     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         188    0         8      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       192    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         202    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         956    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      56        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4364   334       630    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001260 records: 2, size/record: 8, table size: 16
	.data: load addr=00001238, load size=00000014 bytes, run addr=20200000, run size=00000043 bytes, compression=lzss
	.bss: load addr=00001258, load size=00000008 bytes, run addr=20200044, run size=00000033 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000124c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000b0f  ADC0_IRQHandler               
00000b0f  ADC1_IRQHandler               
00000b0f  AES_IRQHandler                
000011c6  C$$EXIT                       
00000b0f  CANFD0_IRQHandler             
00000b0f  DAC0_IRQHandler               
000011b7  DL_Common_delayCycles         
00000d05  DL_I2C_fillControllerTXFIFO   
00000fb9  DL_I2C_flushControllerTXFIFO  
00001105  DL_I2C_setClockConfig         
00000b0f  DMA_IRQHandler                
00000b0f  Default_Handler               
00000b0f  GROUP0_IRQHandler             
00001191  GROUP1_IRQHandler             
000011c7  HOSTexit                      
00000b0f  HardFault_Handler             
00000b0f  I2C0_IRQHandler               
00000b0f  I2C1_IRQHandler               
00000b0f  NMI_Handler                   
00000b0f  PendSV_Handler                
00000b0f  RTC_IRQHandler                
0000040d  Read_Quad                     
000011cb  Reset_Handler                 
00000b0f  SPI0_IRQHandler               
00000b0f  SPI1_IRQHandler               
00000b0f  SVC_Handler                   
00000c95  SYSCFG_DL_GPIO_init           
00000dbd  SYSCFG_DL_I2C_MPU6050_init    
00000e11  SYSCFG_DL_I2C_OLED_init       
00000e65  SYSCFG_DL_SYSCTL_init         
00001149  SYSCFG_DL_init                
00000eb9  SYSCFG_DL_initPower           
0000112d  SysTick_Handler               
00000ef9  SysTick_Init                  
00000b0f  TIMA0_IRQHandler              
00000b0f  TIMA1_IRQHandler              
00000b0f  TIMG0_IRQHandler              
00000b0f  TIMG12_IRQHandler             
00000b0f  TIMG6_IRQHandler              
00000b0f  TIMG7_IRQHandler              
00000b0f  TIMG8_IRQHandler              
00000b0f  UART0_IRQHandler              
00000b0f  UART1_IRQHandler              
00000b0f  UART2_IRQHandler              
00000b0f  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00001260  __TI_CINIT_Base               
00001270  __TI_CINIT_Limit              
00001270  __TI_CINIT_Warm               
0000124c  __TI_Handler_Table_Base       
00001258  __TI_Handler_Table_Limit      
00001031  __TI_auto_init_nobinit_nopinit
00000b9d  __TI_decompress_lzss          
000011a5  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00001179  __TI_zero_init_nomemset       
00000903  __addsf3                      
00000c21  __aeabi_d2f                   
00000815  __aeabi_dmul                  
00000f79  __aeabi_f2d                   
00000903  __aeabi_fadd                  
00000b11  __aeabi_fmul                  
000008f9  __aeabi_fsub                  
00000ff5  __aeabi_i2f                   
00000d63  __aeabi_idiv0                 
00000c19  __aeabi_memcpy                
00000c19  __aeabi_memcpy4               
00000c19  __aeabi_memcpy8               
00000f39  __aeabi_uidiv                 
00000f39  __aeabi_uidivmod              
ffffffff  __binit__                     
00000f79  __extendsfdf2                 
00000ff5  __floatsisf                   
UNDEFED   __mpu_init                    
00000815  __muldf3                      
0000106d  __muldsi3                     
00000b11  __mulsf3                      
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000008f9  __subsf3                      
00000c21  __truncdfsf2                  
000010dd  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000011cf  _system_pre_init              
000011c1  abort                         
20200054  accel                         
ffffffff  binit                         
000000c1  dmp_read_fifo                 
2020005a  gyro                          
00001226  hw                            
00000000  interruptVectors              
20200034  led_toggle_time               
00000d65  main                          
00000a75  memcpy                        
20200076  more                          
000009d1  mpu_read_fifo_stream          
00000595  mpu_reset_fifo                
000010a9  mspm0_delay_ms                
00001161  mspm0_get_clock_ms            
00000269  mspm0_i2c_read                
000006e5  mspm0_i2c_write               
20200060  pitch                         
20200044  quat                          
00001208  reg                           
20200064  roll                          
20200068  sensor_timestamp              
20200074  sensors                       
2020006c  start_time                    
20200038  sys_tick                      
2020003c  systick_counter               
000011e0  test                          
20200070  yaw                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  dmp_read_fifo                 
00000200  __STACK_SIZE                  
00000269  mspm0_i2c_read                
0000040d  Read_Quad                     
00000595  mpu_reset_fifo                
000006e5  mspm0_i2c_write               
00000815  __aeabi_dmul                  
00000815  __muldf3                      
000008f9  __aeabi_fsub                  
000008f9  __subsf3                      
00000903  __addsf3                      
00000903  __aeabi_fadd                  
000009d1  mpu_read_fifo_stream          
00000a75  memcpy                        
00000b0f  ADC0_IRQHandler               
00000b0f  ADC1_IRQHandler               
00000b0f  AES_IRQHandler                
00000b0f  CANFD0_IRQHandler             
00000b0f  DAC0_IRQHandler               
00000b0f  DMA_IRQHandler                
00000b0f  Default_Handler               
00000b0f  GROUP0_IRQHandler             
00000b0f  HardFault_Handler             
00000b0f  I2C0_IRQHandler               
00000b0f  I2C1_IRQHandler               
00000b0f  NMI_Handler                   
00000b0f  PendSV_Handler                
00000b0f  RTC_IRQHandler                
00000b0f  SPI0_IRQHandler               
00000b0f  SPI1_IRQHandler               
00000b0f  SVC_Handler                   
00000b0f  TIMA0_IRQHandler              
00000b0f  TIMA1_IRQHandler              
00000b0f  TIMG0_IRQHandler              
00000b0f  TIMG12_IRQHandler             
00000b0f  TIMG6_IRQHandler              
00000b0f  TIMG7_IRQHandler              
00000b0f  TIMG8_IRQHandler              
00000b0f  UART0_IRQHandler              
00000b0f  UART1_IRQHandler              
00000b0f  UART2_IRQHandler              
00000b0f  UART3_IRQHandler              
00000b11  __aeabi_fmul                  
00000b11  __mulsf3                      
00000b9d  __TI_decompress_lzss          
00000c19  __aeabi_memcpy                
00000c19  __aeabi_memcpy4               
00000c19  __aeabi_memcpy8               
00000c21  __aeabi_d2f                   
00000c21  __truncdfsf2                  
00000c95  SYSCFG_DL_GPIO_init           
00000d05  DL_I2C_fillControllerTXFIFO   
00000d63  __aeabi_idiv0                 
00000d65  main                          
00000dbd  SYSCFG_DL_I2C_MPU6050_init    
00000e11  SYSCFG_DL_I2C_OLED_init       
00000e65  SYSCFG_DL_SYSCTL_init         
00000eb9  SYSCFG_DL_initPower           
00000ef9  SysTick_Init                  
00000f39  __aeabi_uidiv                 
00000f39  __aeabi_uidivmod              
00000f79  __aeabi_f2d                   
00000f79  __extendsfdf2                 
00000fb9  DL_I2C_flushControllerTXFIFO  
00000ff5  __aeabi_i2f                   
00000ff5  __floatsisf                   
00001031  __TI_auto_init_nobinit_nopinit
0000106d  __muldsi3                     
000010a9  mspm0_delay_ms                
000010dd  _c_int00_noargs               
00001105  DL_I2C_setClockConfig         
0000112d  SysTick_Handler               
00001149  SYSCFG_DL_init                
00001161  mspm0_get_clock_ms            
00001179  __TI_zero_init_nomemset       
00001191  GROUP1_IRQHandler             
000011a5  __TI_decompress_none          
000011b7  DL_Common_delayCycles         
000011c1  abort                         
000011c6  C$$EXIT                       
000011c7  HOSTexit                      
000011cb  Reset_Handler                 
000011cf  _system_pre_init              
000011e0  test                          
00001208  reg                           
00001226  hw                            
0000124c  __TI_Handler_Table_Base       
00001258  __TI_Handler_Table_Limit      
00001260  __TI_CINIT_Base               
00001270  __TI_CINIT_Limit              
00001270  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200034  led_toggle_time               
20200038  sys_tick                      
2020003c  systick_counter               
20200044  quat                          
20200054  accel                         
2020005a  gyro                          
20200060  pitch                         
20200064  roll                          
20200068  sensor_timestamp              
2020006c  start_time                    
20200070  yaw                           
20200074  sensors                       
20200076  more                          
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[128 symbols]

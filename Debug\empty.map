******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jun 26 21:25:12 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001ded


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00002030  0001dfd0  R  X
  SRAM                  20200000   00008000  00000272  00007d8e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002030   00002030    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001ea0   00001ea0    r-x .text
  00001f60    00001f60    00000098   00000098    r-- .rodata
  00001ff8    00001ff8    00000038   00000038    r-- .cinit
20200000    20200000    00000073   00000000    rw-
  20200000    20200000    0000003f   00000000    rw- .data
  20200040    20200040    00000033   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001ea0     
                  000000c0    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00000424    000002f8            : s_atan.c.obj (.text.atan)
                  0000071c    00000210     mpu6050.o (.text.Read_Quad)
                  0000092c    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00000ad4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000c66    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000c68    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00000df0    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00000f60    00000154     mspm0_i2c.o (.text.mspm0_i2c_read)
                  000010b4    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  00001204    0000012c     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00001330    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000143c    000000e4                            : muldf3.S.obj (.text.__muldf3)
                  00001520    000000d8                            : addsf3.S.obj (.text)
                  000015f8    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  0000169c    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00001736    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001738    0000008c                            : mulsf3.S.obj (.text.__mulsf3)
                  000017c4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001840    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000018b4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000018be    00000002     --HOLE-- [fill = 0]
                  000018c0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00001934    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000019a4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001a0c    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001a6e    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00001acc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00001b20    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00001b74    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001bc8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001c08    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001c48    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001c88    00000040     empty.o (.text.main)
                  00001cc8    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00001d04    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001d40    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001d7c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00001db6    00000002     --HOLE-- [fill = 0]
                  00001db8    00000034     clock.o (.text.mspm0_delay_ms)
                  00001dec    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001e14    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00001e3a    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001e52    00000002     --HOLE-- [fill = 0]
                  00001e54    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00001e6c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001e82    00000002     --HOLE-- [fill = 0]
                  00001e84    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  00001e98    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00001eaa    00000002     --HOLE-- [fill = 0]
                  00001eac    00000010     interrupt.o (.text.SysTick_Handler)
                  00001ebc    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00001ec6    00000002     --HOLE-- [fill = 0]
                  00001ec8    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00001ed8    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00001ee2    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00001eec    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00001ef6    00000002     --HOLE-- [fill = 0]
                  00001ef8    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00001f08    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00001f10    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00001f18    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001f20    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00001f26    00000002     --HOLE-- [fill = 0]
                  00001f28    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00001f38    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00001f3e    00000006            : exit.c.obj (.text:abort)
                  00001f44    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001f48    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00001f4c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001f50    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001f54    0000000c     --HOLE-- [fill = 0]

.cinit     0    00001ff8    00000038     
                  00001ff8    00000013     (.cinit..data.load) [load image, compression = lzss]
                  0000200b    00000001     --HOLE-- [fill = 0]
                  0000200c    0000000c     (__TI_handler_table)
                  00002018    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002020    00000010     (__TI_cinit_table)

.rodata    0    00001f60    00000098     
                  00001f60    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00001fa0    00000028     inv_mpu.o (.rodata.test)
                  00001fc8    0000001e     inv_mpu.o (.rodata.reg)
                  00001fe6    0000000c     inv_mpu.o (.rodata.hw)
                  00001ff2    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00001ff4    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00001ff6    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000003f     UNINITIALIZED
                  20200000    0000002c     inv_mpu.o (.data.st)
                  2020002c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200030    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200034    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  20200038    00000004     clock.o (.data.sys_tick)
                  2020003c    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  2020003e    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)

.bss       0    20200040    00000033     UNINITIALIZED
                  20200040    00000010     (.common:quat)
                  20200050    00000006     (.common:accel)
                  20200056    00000006     (.common:gyro)
                  2020005c    00000004     (.common:pitch)
                  20200060    00000004     (.common:roll)
                  20200064    00000004     (.common:sensor_timestamp)
                  20200068    00000004     (.common:start_time)
                  2020006c    00000004     (.common:yaw)
                  20200070    00000002     (.common:sensors)
                  20200072    00000001     (.common:more)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             452    4         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        64     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         522    196       0      
                                                              
    .\BSP\MPU6050\
       mspm0_i2c.o                    640    0         0      
       inv_mpu.o                      500    82        44     
       mpu6050.o                      528    0         47     
       inv_mpu_dmp_motion_driver.o    424    0         11     
    +--+------------------------------+------+---------+---------+
       Total:                         2092   82        102    
                                                              
    .\System\
       clock.o                        76     0         8      
       interrupt.o                    36     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         112    0         8      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       192    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         202    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_asin.c.obj                   908    0         0      
       s_atan.c.obj                   784    64        0      
       e_atan2.c.obj                  392    0         0      
       e_sqrt.c.obj                   368    0         0      
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       aeabi_portable.c.obj           8      0         4      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2888   64        4      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418    0         0      
       divdf3.S.obj                   284    0         0      
       muldf3.S.obj                   244    0         0      
       comparedf2.c.obj               220    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_dcmp.S.obj               98     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1992   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      55        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   7812   397       626    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002020 records: 2, size/record: 8, table size: 16
	.data: load addr=00001ff8, load size=00000013 bytes, run addr=20200000, run size=0000003f bytes, compression=lzss
	.bss: load addr=00002018, load size=00000008 bytes, run addr=20200040, run size=00000033 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000200c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000143d     00001ec8     00001ec4   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00001ee0          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00001eea          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00001f0e          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00001f3c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00001331     00001ef8     00001ef4   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00000adf     00001f28     00001f24   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00001f4a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)

[3 trampolines]
[8 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000c67  ADC0_IRQHandler               
00000c67  ADC1_IRQHandler               
00000c67  AES_IRQHandler                
00001f44  C$$EXIT                       
00000c67  CANFD0_IRQHandler             
00000c67  DAC0_IRQHandler               
000018b5  DL_Common_delayCycles         
00001a6f  DL_I2C_fillControllerTXFIFO   
00001cc9  DL_I2C_flushControllerTXFIFO  
00001e15  DL_I2C_setClockConfig         
00000c67  DMA_IRQHandler                
00000c67  Default_Handler               
00000c67  GROUP0_IRQHandler             
00001e85  GROUP1_IRQHandler             
00001f45  HOSTexit                      
00000c67  HardFault_Handler             
00000c67  I2C0_IRQHandler               
00000c67  I2C1_IRQHandler               
00000c67  NMI_Handler                   
00000c67  PendSV_Handler                
00000c67  RTC_IRQHandler                
0000071d  Read_Quad                     
00001f4d  Reset_Handler                 
00000c67  SPI0_IRQHandler               
00000c67  SPI1_IRQHandler               
00000c67  SVC_Handler                   
00001935  SYSCFG_DL_GPIO_init           
00001acd  SYSCFG_DL_I2C_MPU6050_init    
00001b21  SYSCFG_DL_I2C_OLED_init       
00001b75  SYSCFG_DL_SYSCTL_init         
00001e3b  SYSCFG_DL_init                
00001bc9  SYSCFG_DL_initPower           
00001ead  SysTick_Handler               
00000c67  TIMA0_IRQHandler              
00000c67  TIMA1_IRQHandler              
00000c67  TIMG0_IRQHandler              
00000c67  TIMG12_IRQHandler             
00000c67  TIMG6_IRQHandler              
00000c67  TIMG7_IRQHandler              
00000c67  TIMG8_IRQHandler              
00000c67  UART0_IRQHandler              
00000c67  UART1_IRQHandler              
00000c67  UART2_IRQHandler              
00000c67  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00002020  __TI_CINIT_Base               
00002030  __TI_CINIT_Limit              
00002030  __TI_CINIT_Warm               
0000200c  __TI_Handler_Table_Base       
00002018  __TI_Handler_Table_Limit      
00001d41  __TI_auto_init_nobinit_nopinit
000017c5  __TI_decompress_lzss          
00001e99  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00001e6d  __TI_zero_init_nomemset       
00000adf  __adddf3                      
0000152b  __addsf3                      
000018c1  __aeabi_d2f                   
00000adf  __aeabi_dadd                  
00001a0d  __aeabi_dcmpeq                
00001a49  __aeabi_dcmpge                
00001a5d  __aeabi_dcmpgt                
00001a35  __aeabi_dcmple                
00001a21  __aeabi_dcmplt                
00001331  __aeabi_ddiv                  
0000143d  __aeabi_dmul                  
00000ad5  __aeabi_dsub                  
2020002c  __aeabi_errno                 
00001f11  __aeabi_errno_addr            
00001c49  __aeabi_f2d                   
0000152b  __aeabi_fadd                  
00001739  __aeabi_fmul                  
00001521  __aeabi_fsub                  
00001d05  __aeabi_i2f                   
00001737  __aeabi_idiv0                 
00001f19  __aeabi_memcpy                
00001f19  __aeabi_memcpy4               
00001f19  __aeabi_memcpy8               
00001c09  __aeabi_uidiv                 
00001c09  __aeabi_uidivmod              
ffffffff  __binit__                     
000019a5  __cmpdf2                      
00001331  __divdf3                      
000019a5  __eqdf2                       
00001c49  __extendsfdf2                 
00001d05  __floatsisf                   
00001841  __gedf2                       
00001841  __gtdf2                       
000019a5  __ledf2                       
000019a5  __ltdf2                       
UNDEFED   __mpu_init                    
0000143d  __muldf3                      
00001d7d  __muldsi3                     
00001739  __mulsf3                      
000019a5  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000ad5  __subdf3                      
00001521  __subsf3                      
000018c1  __truncdfsf2                  
00001ded  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00001f51  _system_pre_init              
00001f3f  abort                         
20200050  accel                         
000000c1  asin                          
000000c1  asinl                         
00000425  atan                          
00000c69  atan2                         
00000c69  atan2l                        
00000425  atanl                         
ffffffff  binit                         
0000092d  dmp_read_fifo                 
20200056  gyro                          
00001fe6  hw                            
00000000  interruptVectors              
00001c89  main                          
0000169d  memcpy                        
20200072  more                          
000015f9  mpu_read_fifo_stream          
000010b5  mpu_reset_fifo                
00001db9  mspm0_delay_ms                
00001e55  mspm0_get_clock_ms            
00000f61  mspm0_i2c_read                
00001205  mspm0_i2c_write               
2020005c  pitch                         
20200040  quat                          
00001fc8  reg                           
20200060  roll                          
20200064  sensor_timestamp              
20200070  sensors                       
00000df1  sqrt                          
00000df1  sqrtl                         
20200068  start_time                    
20200038  sys_tick                      
00001fa0  test                          
2020006c  yaw                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  asin                          
000000c1  asinl                         
00000200  __STACK_SIZE                  
00000425  atan                          
00000425  atanl                         
0000071d  Read_Quad                     
0000092d  dmp_read_fifo                 
00000ad5  __aeabi_dsub                  
00000ad5  __subdf3                      
00000adf  __adddf3                      
00000adf  __aeabi_dadd                  
00000c67  ADC0_IRQHandler               
00000c67  ADC1_IRQHandler               
00000c67  AES_IRQHandler                
00000c67  CANFD0_IRQHandler             
00000c67  DAC0_IRQHandler               
00000c67  DMA_IRQHandler                
00000c67  Default_Handler               
00000c67  GROUP0_IRQHandler             
00000c67  HardFault_Handler             
00000c67  I2C0_IRQHandler               
00000c67  I2C1_IRQHandler               
00000c67  NMI_Handler                   
00000c67  PendSV_Handler                
00000c67  RTC_IRQHandler                
00000c67  SPI0_IRQHandler               
00000c67  SPI1_IRQHandler               
00000c67  SVC_Handler                   
00000c67  TIMA0_IRQHandler              
00000c67  TIMA1_IRQHandler              
00000c67  TIMG0_IRQHandler              
00000c67  TIMG12_IRQHandler             
00000c67  TIMG6_IRQHandler              
00000c67  TIMG7_IRQHandler              
00000c67  TIMG8_IRQHandler              
00000c67  UART0_IRQHandler              
00000c67  UART1_IRQHandler              
00000c67  UART2_IRQHandler              
00000c67  UART3_IRQHandler              
00000c69  atan2                         
00000c69  atan2l                        
00000df1  sqrt                          
00000df1  sqrtl                         
00000f61  mspm0_i2c_read                
000010b5  mpu_reset_fifo                
00001205  mspm0_i2c_write               
00001331  __aeabi_ddiv                  
00001331  __divdf3                      
0000143d  __aeabi_dmul                  
0000143d  __muldf3                      
00001521  __aeabi_fsub                  
00001521  __subsf3                      
0000152b  __addsf3                      
0000152b  __aeabi_fadd                  
000015f9  mpu_read_fifo_stream          
0000169d  memcpy                        
00001737  __aeabi_idiv0                 
00001739  __aeabi_fmul                  
00001739  __mulsf3                      
000017c5  __TI_decompress_lzss          
00001841  __gedf2                       
00001841  __gtdf2                       
000018b5  DL_Common_delayCycles         
000018c1  __aeabi_d2f                   
000018c1  __truncdfsf2                  
00001935  SYSCFG_DL_GPIO_init           
000019a5  __cmpdf2                      
000019a5  __eqdf2                       
000019a5  __ledf2                       
000019a5  __ltdf2                       
000019a5  __nedf2                       
00001a0d  __aeabi_dcmpeq                
00001a21  __aeabi_dcmplt                
00001a35  __aeabi_dcmple                
00001a49  __aeabi_dcmpge                
00001a5d  __aeabi_dcmpgt                
00001a6f  DL_I2C_fillControllerTXFIFO   
00001acd  SYSCFG_DL_I2C_MPU6050_init    
00001b21  SYSCFG_DL_I2C_OLED_init       
00001b75  SYSCFG_DL_SYSCTL_init         
00001bc9  SYSCFG_DL_initPower           
00001c09  __aeabi_uidiv                 
00001c09  __aeabi_uidivmod              
00001c49  __aeabi_f2d                   
00001c49  __extendsfdf2                 
00001c89  main                          
00001cc9  DL_I2C_flushControllerTXFIFO  
00001d05  __aeabi_i2f                   
00001d05  __floatsisf                   
00001d41  __TI_auto_init_nobinit_nopinit
00001d7d  __muldsi3                     
00001db9  mspm0_delay_ms                
00001ded  _c_int00_noargs               
00001e15  DL_I2C_setClockConfig         
00001e3b  SYSCFG_DL_init                
00001e55  mspm0_get_clock_ms            
00001e6d  __TI_zero_init_nomemset       
00001e85  GROUP1_IRQHandler             
00001e99  __TI_decompress_none          
00001ead  SysTick_Handler               
00001f11  __aeabi_errno_addr            
00001f19  __aeabi_memcpy                
00001f19  __aeabi_memcpy4               
00001f19  __aeabi_memcpy8               
00001f3f  abort                         
00001f44  C$$EXIT                       
00001f45  HOSTexit                      
00001f4d  Reset_Handler                 
00001f51  _system_pre_init              
00001fa0  test                          
00001fc8  reg                           
00001fe6  hw                            
0000200c  __TI_Handler_Table_Base       
00002018  __TI_Handler_Table_Limit      
00002020  __TI_CINIT_Base               
00002030  __TI_CINIT_Limit              
00002030  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
2020002c  __aeabi_errno                 
20200038  sys_tick                      
20200040  quat                          
20200050  accel                         
20200056  gyro                          
2020005c  pitch                         
20200060  roll                          
20200064  sensor_timestamp              
20200068  start_time                    
2020006c  yaw                           
20200070  sensors                       
20200072  more                          
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[153 symbols]

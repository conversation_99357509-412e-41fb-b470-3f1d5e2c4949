******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jun 26 23:36:56 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003181


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00003e08  0001c1f8  R  X
  SRAM                  20200000   00008000  00000290  00007d70  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003e08   00003e08    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003310   00003310    r-x .text
  000033d0    000033d0    000009f0   000009f0    r-- .rodata
  00003dc0    00003dc0    00000048   00000048    r-- .cinit
20200000    20200000    00000090   00000000    rw-
  20200000    20200000    0000005c   00000000    rw- .data
  2020005c    2020005c    00000034   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003310     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000002c4     oled_hardware_i2c.o (.text.OLED_Init)
                  00000d54    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000f74    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001150    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000012f8    000001a4     mspm0_i2c.o (.text.mspm0_i2c_read)
                  0000149c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000162e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001630    00000188     mpu6050.o (.text.Read_Quad)
                  000017b8    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  00001908    00000144     oled_hardware_i2c.o (.text.OLED_Clear)
                  00001a4c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001b88    00000138     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00001cc0    00000130     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00001df0    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001f10    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000201c    000000e4                            : muldf3.S.obj (.text.__muldf3)
                  00002100    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  000021dc    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000022b4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000238c    000000d0     oled_app.o (.text.oled_task)
                  0000245c    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002500    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000025a2    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000263c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000026c8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002744    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  000027be    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000027c0    00000074                            : comparedf2.c.obj (.text.__gedf2)
                  00002834    0000000c     clock.o (.text.SystemTime_GetMs)
                  00002840    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000028b4    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00002924    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000298c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000029f2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000029f4    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002a56    00000062     libc.a : memset16.S.obj (.text:memset)
                  00002ab8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002b16    00000002     --HOLE-- [fill = 0]
                  00002b18    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00002b74    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00002bcc    00000058            : _printfi.c.obj (.text._pconv_f)
                  00002c24    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002c7a    00000002     --HOLE-- [fill = 0]
                  00002c7c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00002cd0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002d24    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002d78    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00002dca    00000002     --HOLE-- [fill = 0]
                  00002dcc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002e16    00000002     --HOLE-- [fill = 0]
                  00002e18    00000044     scheduler.o (.text.scheduler_run)
                  00002e5c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002e9c    00000040     clock.o (.text.SysTick_Init)
                  00002edc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002f1c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00002f5c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00002f9c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00002fd8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003014    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003050    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000308a    00000002     --HOLE-- [fill = 0]
                  0000308c    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  000030c4    00000034     clock.o (.text.mspm0_delay_ms)
                  000030f8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003128    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003154    0000002c     mpu6050_app.o (.text.mpu6050_get_angles)
                  00003180    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000031a8    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000031ce    00000002     --HOLE-- [fill = 0]
                  000031d0    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000031f4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003216    00000002     --HOLE-- [fill = 0]
                  00003218    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003236    0000001a     empty.o (.text.main)
                  00003250    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003268    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00003280    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00003298    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000032ae    00000002     --HOLE-- [fill = 0]
                  000032b0    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  000032c4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000032d8    00000014     scheduler.o (.text.scheduler_init)
                  000032ec    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00003300    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003312    00000002     --HOLE-- [fill = 0]
                  00003314    00000010     interrupt.o (.text.SysTick_Handler)
                  00003324    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00003334    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00003342    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00003350    0000000c     empty.o (.text.led_blink_task)
                  0000335c    0000000c     mpu6050_app.o (.text.mpu6050_clear_data_ready)
                  00003368    0000000c     mpu6050_app.o (.text.mpu6050_is_data_ready)
                  00003374    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000337e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003388    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00003398    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000033a2    0000000a            : sprintf.c.obj (.text._outc)
                  000033ac    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000033b4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000033bc    00000006     libc.a : exit.c.obj (.text:abort)
                  000033c2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000033c6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000033ca    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000033ce    00000002     --HOLE-- [fill = 0]

.cinit     0    00003dc0    00000048     
                  00003dc0    00000023     (.cinit..data.load) [load image, compression = lzss]
                  00003de3    00000001     --HOLE-- [fill = 0]
                  00003de4    0000000c     (__TI_handler_table)
                  00003df0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003df8    00000010     (__TI_cinit_table)

.rodata    0    000033d0    000009f0     
                  000033d0    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  000039c0    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00003be8    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00003bea    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00003bec    00000004     --HOLE-- [fill = 0]
                  00003bf0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00003cf1    00000003     --HOLE-- [fill = 0]
                  00003cf4    00000028     inv_mpu.o (.rodata.test)
                  00003d1c    0000001e     inv_mpu.o (.rodata.reg)
                  00003d3a    00000015     oled_app.o (.rodata.str1.3320576987535119861.1)
                  00003d4f    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00003d60    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00003d71    0000000f     oled_app.o (.rodata.str1.17288060079586621852.1)
                  00003d80    0000000e     oled_app.o (.rodata.str1.4119433759365661616.1)
                  00003d8e    0000000d     oled_app.o (.rodata.str1.10123847253219111340.1)
                  00003d9b    00000001     --HOLE-- [fill = 0]
                  00003d9c    0000000c     inv_mpu.o (.rodata.hw)
                  00003da8    0000000a     oled_app.o (.rodata.str1.865768355259620247.1)
                  00003db2    0000000e     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000005c     UNINITIALIZED
                  20200000    0000002c     inv_mpu.o (.data.st)
                  2020002c    00000018     scheduler.o (.data.scheduler_task)
                  20200044    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200048    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  2020004c    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  20200050    00000004     oled_app.o (.data.oled_task.counter)
                  20200054    00000004     clock.o (.data.sys_tick)
                  20200058    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  2020005a    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)
                  2020005b    00000001     mpu6050_app.o (.data.mpu6050_data_ready)

.bss       0    2020005c    00000034     UNINITIALIZED
                  2020005c    00000010     (.common:quat)
                  2020006c    00000006     (.common:accel)
                  20200072    00000006     (.common:gyro)
                  20200078    00000004     (.common:pitch)
                  2020007c    00000004     (.common:roll)
                  20200080    00000004     (.common:sensor_timestamp)
                  20200084    00000004     (.common:start_time)
                  20200088    00000004     (.common:yaw)
                  2020008c    00000002     (.common:sensors)
                  2020008e    00000001     (.common:more)
                  2020008f    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             452     4         0      
       startup_mspm0g350x_ticlang.o   6       192       0      
       scheduler.o                    88      0         25     
       empty.o                        38      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         584     196       25     
                                                               
    .\APP\
       oled_app.o                     208     73        4      
       mpu6050_app.o                  68      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         276     73        5      
                                                               
    .\BSP\MPU6050\
       mspm0_i2c.o                    724     0         0      
       inv_mpu.o                      500     82        44     
       mpu6050.o                      392     0         47     
       inv_mpu_dmp_motion_driver.o    424     0         11     
    +--+------------------------------+-------+---------+---------+
       Total:                         2040    82        102    
                                                               
    .\BSP\OLED\
       oled_hardware_i2c.o            1686    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1686    2072      0      
                                                               
    .\System\
       clock.o                        152     0         8      
       interrupt.o                    36      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         188     0         8      
                                                               
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       192     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         202     0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5644    291       4      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2428    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       71        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   13052   2785      656    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003df8 records: 2, size/record: 8, table size: 16
	.data: load addr=00003dc0, load size=00000023 bytes, run addr=20200000, run size=0000005c bytes, compression=lzss
	.bss: load addr=00003df0, load size=00000008 bytes, run addr=2020005c, run size=00000034 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003de4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000149d     00003388     00003386   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000162f  ADC0_IRQHandler               
0000162f  ADC1_IRQHandler               
0000162f  AES_IRQHandler                
000033c2  C$$EXIT                       
0000162f  CANFD0_IRQHandler             
0000162f  DAC0_IRQHandler               
00003375  DL_Common_delayCycles         
00002ab9  DL_I2C_fillControllerTXFIFO   
00002f9d  DL_I2C_flushControllerTXFIFO  
000031a9  DL_I2C_setClockConfig         
0000162f  DMA_IRQHandler                
0000162f  Default_Handler               
0000162f  GROUP0_IRQHandler             
000032b1  GROUP1_IRQHandler             
000033c3  HOSTexit                      
0000162f  HardFault_Handler             
0000162f  I2C0_IRQHandler               
0000162f  I2C1_IRQHandler               
0000162f  NMI_Handler                   
00001909  OLED_Clear                    
00000a91  OLED_Init                     
00002101  OLED_ShowChar                 
00002745  OLED_ShowString               
00001b89  OLED_WR_Byte                  
0000162f  PendSV_Handler                
0000162f  RTC_IRQHandler                
00001631  Read_Quad                     
000033c7  Reset_Handler                 
0000162f  SPI0_IRQHandler               
0000162f  SPI1_IRQHandler               
0000162f  SVC_Handler                   
000028b5  SYSCFG_DL_GPIO_init           
00002c7d  SYSCFG_DL_I2C_MPU6050_init    
00002cd1  SYSCFG_DL_I2C_OLED_init       
00002d25  SYSCFG_DL_SYSCTL_init         
00003251  SYSCFG_DL_init                
00002e5d  SYSCFG_DL_initPower           
00003315  SysTick_Handler               
00002e9d  SysTick_Init                  
00002835  SystemTime_GetMs              
0000162f  TIMA0_IRQHandler              
0000162f  TIMA1_IRQHandler              
0000162f  TIMG0_IRQHandler              
0000162f  TIMG12_IRQHandler             
0000162f  TIMG6_IRQHandler              
0000162f  TIMG7_IRQHandler              
0000162f  TIMG8_IRQHandler              
0000162f  UART0_IRQHandler              
0000162f  UART1_IRQHandler              
0000162f  UART2_IRQHandler              
0000162f  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00003df8  __TI_CINIT_Base               
00003e08  __TI_CINIT_Limit              
00003e08  __TI_CINIT_Warm               
00003de4  __TI_Handler_Table_Base       
00003df0  __TI_Handler_Table_Limit      
00003015  __TI_auto_init_nobinit_nopinit
000026c9  __TI_decompress_lzss          
00003301  __TI_decompress_none          
00002b75  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00003299  __TI_zero_init_nomemset       
000014a7  __adddf3                      
000022bf  __addsf3                      
00003bf0  __aeabi_ctype_table_          
00003bf0  __aeabi_ctype_table_C         
00002841  __aeabi_d2f                   
00002dcd  __aeabi_d2iz                  
000014a7  __aeabi_dadd                  
000029f5  __aeabi_dcmpeq                
00002a31  __aeabi_dcmpge                
00002a45  __aeabi_dcmpgt                
00002a1d  __aeabi_dcmple                
00002a09  __aeabi_dcmplt                
00001f11  __aeabi_ddiv                  
0000201d  __aeabi_dmul                  
0000149d  __aeabi_dsub                  
20200044  __aeabi_errno                 
000033ad  __aeabi_errno_addr            
00002f1d  __aeabi_f2d                   
000022bf  __aeabi_fadd                  
0000263d  __aeabi_fmul                  
000022b5  __aeabi_fsub                  
00003129  __aeabi_i2d                   
00002fd9  __aeabi_i2f                   
00002c25  __aeabi_idiv                  
000027bf  __aeabi_idiv0                 
00002c25  __aeabi_idivmod               
000029f3  __aeabi_ldiv0                 
00003219  __aeabi_llsl                  
000031d1  __aeabi_lmul                  
000033b5  __aeabi_memcpy                
000033b5  __aeabi_memcpy4               
000033b5  __aeabi_memcpy8               
00003335  __aeabi_memset                
00003335  __aeabi_memset4               
00003335  __aeabi_memset8               
00002edd  __aeabi_uidiv                 
00002edd  __aeabi_uidivmod              
000032c5  __aeabi_uldivmod              
00003219  __ashldi3                     
ffffffff  __binit__                     
00002925  __cmpdf2                      
00001f11  __divdf3                      
00002925  __eqdf2                       
00002f1d  __extendsfdf2                 
00002dcd  __fixdfsi                     
00003129  __floatsidf                   
00002fd9  __floatsisf                   
000027c1  __gedf2                       
000027c1  __gtdf2                       
00002925  __ledf2                       
00002925  __ltdf2                       
UNDEFED   __mpu_init                    
0000201d  __muldf3                      
000031d1  __muldi3                      
00003051  __muldsi3                     
0000263d  __mulsf3                      
00002925  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
0000149d  __subdf3                      
000022b5  __subsf3                      
00002841  __truncdfsf2                  
00002501  __udivmoddi4                  
00003181  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000033cb  _system_pre_init              
000033bd  abort                         
2020006c  accel                         
000039c0  asc2_0806                     
000033d0  asc2_1608                     
00002f5d  atoi                          
ffffffff  binit                         
00001151  dmp_read_fifo                 
00002b19  frexp                         
00002b19  frexpl                        
20200072  gyro                          
00003d9c  hw                            
00000000  interruptVectors              
000021dd  ldexp                         
000021dd  ldexpl                        
00003351  led_blink_task                
00003237  main                          
000031f5  memccpy                       
000025a3  memcpy                        
00002a57  memset                        
2020008e  more                          
0000335d  mpu6050_clear_data_ready      
00003155  mpu6050_get_angles            
00003369  mpu6050_is_data_ready         
0000245d  mpu_read_fifo_stream          
000017b9  mpu_reset_fifo                
000030c5  mspm0_delay_ms                
00003281  mspm0_get_clock_ms            
000012f9  mspm0_i2c_read                
00001cc1  mspm0_i2c_write               
0000238d  oled_task                     
20200078  pitch                         
2020005c  quat                          
00003d1c  reg                           
2020007c  roll                          
000021dd  scalbn                        
000021dd  scalbnl                       
000032d9  scheduler_init                
00002e19  scheduler_run                 
20200080  sensor_timestamp              
2020008c  sensors                       
0000308d  sprintf                       
20200084  start_time                    
20200054  sys_tick                      
2020008f  task_num                      
00003cf4  test                          
00003325  wcslen                        
20200088  yaw                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000a91  OLED_Init                     
00001151  dmp_read_fifo                 
000012f9  mspm0_i2c_read                
0000149d  __aeabi_dsub                  
0000149d  __subdf3                      
000014a7  __adddf3                      
000014a7  __aeabi_dadd                  
0000162f  ADC0_IRQHandler               
0000162f  ADC1_IRQHandler               
0000162f  AES_IRQHandler                
0000162f  CANFD0_IRQHandler             
0000162f  DAC0_IRQHandler               
0000162f  DMA_IRQHandler                
0000162f  Default_Handler               
0000162f  GROUP0_IRQHandler             
0000162f  HardFault_Handler             
0000162f  I2C0_IRQHandler               
0000162f  I2C1_IRQHandler               
0000162f  NMI_Handler                   
0000162f  PendSV_Handler                
0000162f  RTC_IRQHandler                
0000162f  SPI0_IRQHandler               
0000162f  SPI1_IRQHandler               
0000162f  SVC_Handler                   
0000162f  TIMA0_IRQHandler              
0000162f  TIMA1_IRQHandler              
0000162f  TIMG0_IRQHandler              
0000162f  TIMG12_IRQHandler             
0000162f  TIMG6_IRQHandler              
0000162f  TIMG7_IRQHandler              
0000162f  TIMG8_IRQHandler              
0000162f  UART0_IRQHandler              
0000162f  UART1_IRQHandler              
0000162f  UART2_IRQHandler              
0000162f  UART3_IRQHandler              
00001631  Read_Quad                     
000017b9  mpu_reset_fifo                
00001909  OLED_Clear                    
00001b89  OLED_WR_Byte                  
00001cc1  mspm0_i2c_write               
00001f11  __aeabi_ddiv                  
00001f11  __divdf3                      
0000201d  __aeabi_dmul                  
0000201d  __muldf3                      
00002101  OLED_ShowChar                 
000021dd  ldexp                         
000021dd  ldexpl                        
000021dd  scalbn                        
000021dd  scalbnl                       
000022b5  __aeabi_fsub                  
000022b5  __subsf3                      
000022bf  __addsf3                      
000022bf  __aeabi_fadd                  
0000238d  oled_task                     
0000245d  mpu_read_fifo_stream          
00002501  __udivmoddi4                  
000025a3  memcpy                        
0000263d  __aeabi_fmul                  
0000263d  __mulsf3                      
000026c9  __TI_decompress_lzss          
00002745  OLED_ShowString               
000027bf  __aeabi_idiv0                 
000027c1  __gedf2                       
000027c1  __gtdf2                       
00002835  SystemTime_GetMs              
00002841  __aeabi_d2f                   
00002841  __truncdfsf2                  
000028b5  SYSCFG_DL_GPIO_init           
00002925  __cmpdf2                      
00002925  __eqdf2                       
00002925  __ledf2                       
00002925  __ltdf2                       
00002925  __nedf2                       
000029f3  __aeabi_ldiv0                 
000029f5  __aeabi_dcmpeq                
00002a09  __aeabi_dcmplt                
00002a1d  __aeabi_dcmple                
00002a31  __aeabi_dcmpge                
00002a45  __aeabi_dcmpgt                
00002a57  memset                        
00002ab9  DL_I2C_fillControllerTXFIFO   
00002b19  frexp                         
00002b19  frexpl                        
00002b75  __TI_ltoa                     
00002c25  __aeabi_idiv                  
00002c25  __aeabi_idivmod               
00002c7d  SYSCFG_DL_I2C_MPU6050_init    
00002cd1  SYSCFG_DL_I2C_OLED_init       
00002d25  SYSCFG_DL_SYSCTL_init         
00002dcd  __aeabi_d2iz                  
00002dcd  __fixdfsi                     
00002e19  scheduler_run                 
00002e5d  SYSCFG_DL_initPower           
00002e9d  SysTick_Init                  
00002edd  __aeabi_uidiv                 
00002edd  __aeabi_uidivmod              
00002f1d  __aeabi_f2d                   
00002f1d  __extendsfdf2                 
00002f5d  atoi                          
00002f9d  DL_I2C_flushControllerTXFIFO  
00002fd9  __aeabi_i2f                   
00002fd9  __floatsisf                   
00003015  __TI_auto_init_nobinit_nopinit
00003051  __muldsi3                     
0000308d  sprintf                       
000030c5  mspm0_delay_ms                
00003129  __aeabi_i2d                   
00003129  __floatsidf                   
00003155  mpu6050_get_angles            
00003181  _c_int00_noargs               
000031a9  DL_I2C_setClockConfig         
000031d1  __aeabi_lmul                  
000031d1  __muldi3                      
000031f5  memccpy                       
00003219  __aeabi_llsl                  
00003219  __ashldi3                     
00003237  main                          
00003251  SYSCFG_DL_init                
00003281  mspm0_get_clock_ms            
00003299  __TI_zero_init_nomemset       
000032b1  GROUP1_IRQHandler             
000032c5  __aeabi_uldivmod              
000032d9  scheduler_init                
00003301  __TI_decompress_none          
00003315  SysTick_Handler               
00003325  wcslen                        
00003335  __aeabi_memset                
00003335  __aeabi_memset4               
00003335  __aeabi_memset8               
00003351  led_blink_task                
0000335d  mpu6050_clear_data_ready      
00003369  mpu6050_is_data_ready         
00003375  DL_Common_delayCycles         
000033ad  __aeabi_errno_addr            
000033b5  __aeabi_memcpy                
000033b5  __aeabi_memcpy4               
000033b5  __aeabi_memcpy8               
000033bd  abort                         
000033c2  C$$EXIT                       
000033c3  HOSTexit                      
000033c7  Reset_Handler                 
000033cb  _system_pre_init              
000033d0  asc2_1608                     
000039c0  asc2_0806                     
00003bf0  __aeabi_ctype_table_          
00003bf0  __aeabi_ctype_table_C         
00003cf4  test                          
00003d1c  reg                           
00003d9c  hw                            
00003de4  __TI_Handler_Table_Base       
00003df0  __TI_Handler_Table_Limit      
00003df8  __TI_CINIT_Base               
00003e08  __TI_CINIT_Limit              
00003e08  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200044  __aeabi_errno                 
20200054  sys_tick                      
2020005c  quat                          
2020006c  accel                         
20200072  gyro                          
20200078  pitch                         
2020007c  roll                          
20200080  sensor_timestamp              
20200084  start_time                    
20200088  yaw                           
2020008c  sensors                       
2020008e  more                          
2020008f  task_num                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[193 symbols]

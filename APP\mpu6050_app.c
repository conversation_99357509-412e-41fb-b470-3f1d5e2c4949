#include "mydefine.h"
#include "System/clock.h"

// MPU6050数据更新标志
static volatile bool mpu6050_data_ready = false;

// MPU6050任务函数
void mpu6050_task(void)
{
    static uint32_t last_update_time = 0;
    uint32_t current_time;

    // 获取当前时间
    mspm0_get_clock_ms(&current_time);

    // 每20ms读取一次MPU6050数据 (50Hz)
    if (current_time - last_update_time >= 20)
    {
        if (Read_Quad() == 0)
        {
            mpu6050_data_ready = true;
        }
        last_update_time = current_time;
    }
}

// 获取MPU6050数据状态
bool mpu6050_is_data_ready(void)
{
    return mpu6050_data_ready;
}

// 清除数据就绪标志
void mpu6050_clear_data_ready(void)
{
    mpu6050_data_ready = false;
}

// 获取姿态角度数据
void mpu6050_get_angles(float *pitch_val, float *roll_val, float *yaw_val)
{
    if (pitch_val) *pitch_val = pitch;
    if (roll_val) *roll_val = roll;
    if (yaw_val) *yaw_val = yaw;
}

// 获取原始陀螺仪数据
void mpu6050_get_gyro_data(short *gyro_x, short *gyro_y, short *gyro_z)
{
    if (gyro_x) *gyro_x = gyro[0];
    if (gyro_y) *gyro_y = gyro[1];
    if (gyro_z) *gyro_z = gyro[2];
}

// 获取原始加速度计数据
void mpu6050_get_accel_data(short *accel_x, short *accel_y, short *accel_z)
{
    if (accel_x) *accel_x = accel[0];
    if (accel_y) *accel_y = accel[1];
    if (accel_z) *accel_z = accel[2];
}
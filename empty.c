/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "mydefine.h"

// LED闪烁任务函数
void led_blink_task(void);

// LED闪烁任务实现
void led_blink_task(void)
{
    DL_GPIO_togglePins(Led_PORT, Led_PIN_A3_PIN);
}

int main(void)
{
    SYSCFG_DL_init();

    // 先直接测试LED，不使用调度器
    while (1)
    {
        // 直接控制LED闪烁
        DL_GPIO_togglePins(Led_PORT, Led_PIN_A3_PIN);

        // 简单延时
        for(volatile uint32_t i = 0; i < 1000000; i++);
    }

    // 下面的代码暂时不执行
    /*
    // 初始化任务调度器（包括SysTick）
    scheduler_init();

    // 先测试基本LED功能，暂时注释掉OLED
    // OLED_Init();
    // 暂时注释掉MPU6050初始化，先确保基本系统正常
    // MPU6050_Init();

    while (1)
    {
        // 运行任务调度器
        scheduler_run();
    }
    */
}
